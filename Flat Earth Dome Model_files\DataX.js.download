// (C) http://walter.bislins.ch/doku/DataX

var DataX={LineBreak:'\n',Indent:'  ',DefaultPrec:7,AppName:'AppState',AppObj:null,AppMetaData:null,BeforeSetStateCallback:null,AfterSetStateCallback:null,AssignApp:function(appName,appObj,appMetaData,beforeSetStateCallback,afterSetStateCallback){this.AppName=appName;this.AppObj=appObj;this.AppMetaData=appMetaData;this.BeforeSetStateCallback=xDefFunc(beforeSetStateCallback,null);this.AfterSetStateCallback=xDefFunc(afterSetStateCallback,null);},AppStateToStream:function(overrideCompact){var compact=xDefBool(this.AppMetaData.Compact,false);if(overrideCompact)compact=true;var defPrec=xDefNum(this.AppMetaData.DefaultPrec,this.DefaultPrec);this.ResetStream();this.PropToStream(this.AppObj,{Type:'obj',ObjType:this.AppMetaData.Properties},compact,defPrec);return this.ZipStream(this.StreamStr);},StreamToAppState:function(streamStr,callAfterSetStateCallback){callAfterSetStateCallback=xDefBool(callAfterSetStateCallback,false);this.ResetStream(this.UnzipStream(streamStr));this.StreamToObj(this.AppObj,this.AppMetaData.Properties);if(callAfterSetStateCallback&&this.AfterSetStateCallback){this.AfterSetStateCallback();}},AppStateToJson:function(){var compact=xDefBool(this.AppMetaData.Compact,false);var defPrec=xDefNum(this.AppMetaData.DefaultPrec,this.DefaultPrec);var jsonStr=this.PropToJson('',this.AppObj,{Type:'obj',ObjType:this.AppMetaData.Properties},compact,defPrec,'',false);if(this.AppName!='')jsonStr=this.AppName+' = '+jsonStr;return jsonStr;},JsonToAppState:function(jsonStr){var key1=this.AppName+' = ';var p=jsonStr.indexOf(key1);if(p==0){jsonStr=jsonStr.substring(key1.length);}else{p=jsonStr.indexOf('{');if(p!=0){alert('Invalid Data format');return false;}}
var jsonObj;try{jsonObj=JSON.parse(jsonStr);}catch(e){alert(e.description);return false;}
this.JsonObjToObj(this.AppObj,this.AppMetaData.Properties,jsonObj);return true;},SaveRestoreDomObj:null,SaveRestoreBeforeSetStateCallback:null,SaveRestoreAfterSetStateCallback:null,AssignSaveRestoreDomObj:function(id,beforeSetStateCallback,afterSetStateCallback){if(!id)id='SaveRestoreDomObj';this.SaveRestoreBeforeSetStateCallback=xDefFunc(beforeSetStateCallback,null);this.SaveRestoreAfterSetStateCallback=xDefFunc(afterSetStateCallback,null);var me=this;xOnLoad(function(){me.SaveRestoreDomObj=xElement(id);if(!me.SaveRestoreDomObj)return;xAddEvent(me.SaveRestoreDomObj,'keydown',function(e){me.OnSaveRestoreDomObjKeyDown.call(me,e);});me.RestoreAppStateFromCookie();});},CompactSaveRestoreDomObj:function(){if(!this.SaveRestoreDomObj)return false;var tc=new xTextControl(this.SaveRestoreDomObj);var cursorPos=tc.SelStart;var stateStr=tc.Text;if(stateStr=='')return false;tc.SetText(this.CompactJson(stateStr));tc.SetCaretPos(0);this.SaveRestoreDomObj.focus();return true;},CompactJson:function(stateStr){return stateStr.replace(/[\n ]+/g,' ');},ClearSaveRestoreDomObj:function(){if(!this.SaveRestoreDomObj)return;this.SaveRestoreDomObj.value='';this.SaveRestoreDomObj.focus();},SetupUrlStateHandler:function(jumpAnchor,delay){xOnLoad(function(){DataX.SetAppStateFromUrl(jumpAnchor,delay);});},GetAppState:function(compact,getStateStrOnly){compact=xDefBool(compact,false);getStateStrOnly=xDefBool(getStateStrOnly,false);var jsonStr=this.AppStateToJson(this.AppName,this.AppObj,this.AppMetaData);if(compact){jsonStr=this.CompactJson(jsonStr);}
if(!getStateStrOnly&&this.SaveRestoreDomObj){this.SetSaveRestoreDomObjText(jsonStr);var streamStr=this.AppStateToStream();this.SaveAppStateToCookie(streamStr);}
return jsonStr;},GetAppStateUrl:function(pageUrl,getStateStrOnly){pageUrl=xDefStr(pageUrl,'');getStateStrOnly=xDefBool(getStateStrOnly,false);var oldCompact=this.AppMetaData.Compact;this.AppMetaData.Compact=true;var streamStr=this.AppStateToStream();var url=this.StreamToUrl(pageUrl,streamStr);if(!getStateStrOnly&&this.SaveRestoreDomObj){this.SetSaveRestoreDomObjText(url,true);this.SaveAppStateToCookie(streamStr);}
this.AppMetaData.Compact=oldCompact;return url;},SaveAppStateToCookie:function(streamStr){if(!this.SaveRestoreDomObj||streamStr=='')return;xSetCookie(this.AppName+'AppState',streamStr,30);},RestoreAppStateFromCookie:function(){if(!this.SaveRestoreDomObj)return;var streamStr=xGetCookie(this.AppName+'AppState');if(!streamStr||streamStr=='')return;streamStr=this.AppName+'?state='+encodeURIComponent(streamStr);this.SetSaveRestoreDomObjText(streamStr,false);},SetSaveRestoreDomObjText:function(str,focus){if(!this.SaveRestoreDomObj)return;var tc=new xTextControl(this.SaveRestoreDomObj);tc.SetText(str);if(focus){tc.SetCaretPos(0);this.SaveRestoreDomObj.focus();}},StreamToUrl:function(pageUrl,streamStr){return pageUrl+'&state='+encodeURIComponent(streamStr);},SetAppState:function(stateStr,callCB){callCB=xDefBool(callCB,true);if(xStr(stateStr)){return this.SetAppStateFromStr(stateStr,callCB);}
stateStr=this.GetSaveRestoreDomObjText(true);return this.SetAppStateFromStr(stateStr,callCB);},GetSaveRestoreDomObjText:function(focus){if(!this.SaveRestoreDomObj)return'';var tc=new xTextControl(this.SaveRestoreDomObj);var cursorPos=tc.SelStart;var str=tc.Text;if(focus){if(cursorPos>=str.length-1)cursorPos=0;tc.SetCaretPos(cursorPos);this.SaveRestoreDomObj.focus();}
return str;},SetAppStateFromUrl:function(jumpAnchor,delay){jumpAnchor=xDefStr(jumpAnchor,'');delay=xDefNum(delay,1000);var stateStr=this.GetUrlStr('state');if(stateStr=='')return;if(jumpAnchor!=''){location.hash='#'+jumpAnchor;}
setTimeout(function(){DataX.StreamToAppState(stateStr,true);},1000);},SetAppStateFromStr:function(stateStr,callCB){if(stateStr=='')return false;callCB=xDefBool(callCB,true);if(callCB&&this.BeforeSetStateCallback){this.BeforeSetStateCallback();}
if(stateStr.indexOf(this.AppName+' = ')>=0){if(!this.JsonToAppState(stateStr))return false;}else if((stateStr.indexOf('http://')>=0)||(stateStr.indexOf('https://')>=0)){if(!this.UrlToAppState(stateStr))return false;}else if(stateStr.indexOf(this.AppName+'?')>=0){if(!this.UrlToAppState(stateStr))return false;}else{alert('Invalid Data Format');return false;}
if(callCB&&this.AfterSetStateCallback){this.AfterSetStateCallback();}
return true;},UrlToAppState:function(url){this.ParseUrl(url);var stateStr=this.GetUrlStr('state');if(stateStr=='')return false;this.StreamToAppState(stateStr);return true;},UrlParams:null,ParseUrl:function(url){if(!xStr(url)&&this.UrlParams)return;this.UrlParams={};url=xDefStr(url,location.href);var pos=url.indexOf('?');if(pos<0)return;var paramListStr=url.substr(pos+1);if(paramListStr=='')return;var pos=paramListStr.indexOf('#');if(pos>=0)paramListStr=paramListStr.substr(0,pos);if(paramListStr=='')return;var paramList=paramListStr.split('&');for(i=0;i<paramList.length;i++){var paramStr=paramList[i];var pos=paramStr.indexOf('=');if(pos>0){var name=paramStr.substr(0,pos).toLowerCase();var value=paramStr.substr(pos+1);try{value=decodeURIComponent(value);}catch(err){}
this.UrlParams[name]=value;}}},GetUrlStr:function(name,def){this.ParseUrl();name=name.toLowerCase();if(xDef(this.UrlParams[name]))return this.UrlParams[name];return xDef(def)?def:'';},GetUrlInt:function(name,def){this.ParseUrl();var param=this.GetUrlStr(name);if(param!=''){var val=parseInt(param);if(!isNaN(val))return val;}
return xDef(def)?def:'';},GetUrlNum:function(name,def){this.ParseUrl();var param=this.GetUrlStr(name);if(param!=''){var val=parseFloat(param);if(!isNaN(val))return val;}
return xDef(def)?def:'';},FormatNum:function(num,prec){var str=num.toPrecision(prec);str=str.replace(/(\.\d*?)0+(e|E|$)/,"$1$2");str=str.replace(/\.(e|E|$)/,"$1");return str;},PropToJson:function(propName,val,prop,compact,defPrec,indent,cont){if(!xStr(prop.Type))return'';var propType=prop.Type;var json=indent;var sep=cont?',':'';if(propName!=''){json+='"'+propName+'": ';}
if(propType=='bool'||propType=='int'||propType=='int,null'){if(!compact||!xDef(prop.Default)||val!=prop.Default){return json+val+sep+this.LineBreak;}}else if(propType=='num'||propType=='num,null'){if(!compact||!xDef(prop.Default)||val!=prop.Default){if(val==null){return json+val+sep+this.LineBreak;}
var format=xDefStr(prop.Format,'prec');var prec=xDefNum(prop.Prec,defPrec);var valStr;if(format=='fix'){valStr=val.toFixed(prec);}else{valStr=this.FormatNum(val,prec);}
return json+valStr+sep+this.LineBreak;}}else if(propType=='str'){if(!compact||!xDef(prop.Default)||val!=prop.Default){var s=val.replace(/\"/g,'\\"');return json+'"'+s+'"'+sep+this.LineBreak;}}else if(propType=='arr'){if(!xDef(prop.ArrayType))return'';var arrType=prop.ArrayType;if(!xArray(arrType))arrType=[arrType];var nTypes=arrType.length;var arrSize=val.length;if(xNum(prop.Size)){if(prop.Size<arrSize)arrSize=prop.Size;}
json+='[';if(arrSize>0)json+=this.LineBreak;var eleProp={Type:null};for(var i=0;i<arrSize;i++){var eleType=arrType[i%nTypes];if(xObj(eleType)){if(xDef(eleType.ObjType)){var s=this.PropToJson('',val[i],eleType,compact,defPrec,indent+this.Indent,i<arrSize-1);}}else{eleProp.Type=eleType;var s=this.PropToJson('',val[i],eleProp,false,defPrec,indent+this.Indent,i<arrSize-1);}
if(s!='')json+=s;}
json+=indent+']'+sep+this.LineBreak;return json;}else if(propType=='obj'){json+='{'+this.LineBreak;if(!xArray(prop.ObjType))return'';if(xNum(prop.DefaultPrec))defPrec=prop.DefaultPrec;var props=prop.ObjType;var nProps=props.length;for(var i=0;i<nProps;i++){var propi=props[i];if(xObj(propi)&&xStr(propi.Name)){var s=this.PropToJson(propi.Name,val[propi.Name],propi,compact,defPrec,indent+this.Indent,i<nProps-1);if(s!='')json+=s;}}
json+=indent+'}'+sep+this.LineBreak;return json;}
return'';},JsonObjToObj:function(obj,props,jsonObj){var nProps=props.length;for(var i=0;i<nProps;i++){this.JsonPropToObj(obj,props[i],jsonObj);}},JsonPropToObj:function(obj,prop,jsonObj){var propName=prop.Name;var value;if(xDef(jsonObj[propName])){value=jsonObj[propName];}else if(xDef(prop.Default)){value=prop.Default;}
if(!xDef(value))return;if(!xDef(prop.Type))return;var propType=prop.Type;var typeMatch=false;if(propType=='int'||propType=='num'){typeMatch=xNum(value);}else if(propType=='int,null'||propType=='num,null'){typeMatch=xNum(value)||value==null;}else if(propType=='str'){typeMatch=xStr(value);}else if(propType=='bool'){typeMatch=xBool(value);}else if(propType=='arr'){typeMatch=xArray(value);}else if(propType=='obj'){typeMatch=xObjOrNull(value);}
if(!typeMatch)return;if(propType=='arr'){if(!xDef(prop.ArrayType))return;var arrType=prop.ArrayType;if(!xArray(arrType))arrType=[arrType];var arrTypeSize=arrType.length;var objArr=obj[propName];var arrSize=objArr.length;if(!xNum(prop.Size)){objArr.length=0;arrSize=value.length;}
if(value.length<arrSize)arrSize=value.length;var arrProp={Name:null,Type:null};for(var i=0;i<arrSize;i++){var eleType=arrType[i%arrTypeSize];if(xObj(eleType)){if(xDef(eleType.ObjType)){if(i>=objArr.length||!xDef(objArr[i])){if(xFunc(eleType.Create)){objArr[i]=eleType.Create();}else{objArr[i]={};}}
this.JsonObjToObj(objArr[i],eleType.ObjType,value[i]);if(xFunc(eleType.Init)){eleType.Init(objArr[i]);}}}else{arrProp.Name=i;arrProp.Type=eleType;this.JsonPropToObj(objArr,arrProp,value);}}}else if(propType=='obj'){if(xDef(prop.ObjType)){this.JsonObjToObj(obj[propName],prop.ObjType,value);}}else{obj[propName]=value;}},ZipStream:function(s){s=s.replace(/-/g,'-0');s=s.replace(/~{1,9}/g,function(match){return'-'+(match.length);});s=s.replace(/\-0/g,'~');s=s.replace(/ /g,'--');s=s.replace(/%2D/g,'---');return'-'+s;},UnzipStream:function(s){if(s.indexOf('-')!=0)return this.UnzipStreamOld(s);s=s.substr(1);s=s.replace(/---/g,'%2D');s=s.replace(/--/g,' ');s=s.replace(/~/g,'-0');s=s.replace(/\-([1-9])/g,function(match,p1){var ZipChars='~~~~~~~~~~~';var n=parseInt(p1);return ZipChars.substr(0,n);});s=s.replace(/-0/g,'-');return s;},UnzipStreamOld:function(s){s=s.replace(/!(\d)/g,function(match,p1){var ZipChars='~~~~~~~~~~~';var n=parseInt(p1);if(n==0){return'!';}else{return ZipChars.substr(0,n+2);}});return s;},PropToStream:function(val,prop,compact,defPrec){if(!xDef(prop.Type)){this.StreamAddNone();return;}
var propType=prop.Type;if(propType=='bool'){if(!compact||!xDef(prop.Default)||val!=prop.Default){this.StreamAddInt(val?1:0);}else{this.StreamAddNone();}}else if(propType=='int'||propType=='int,null'){if(!compact||!xDef(prop.Default)||val!=prop.Default){if(propType=='int,null'&&val==null){this.StreamAddNone();}else{this.StreamAddInt(val);}}else{this.StreamAddNone();}}else if(propType=='num'||propType=='num,null'){if(!compact||!xDef(prop.Default)||val!=prop.Default){if(propType=='num,null'&&val==null){this.StreamAddNone();}else{var format=xDefStr(prop.Format,'prec');var prec=xDefNum(prop.Prec,defPrec);var valStr;if(format=='fix'){valStr=val.toFixed(prec);}else{valStr=this.FormatNum(val,prec);}
this.StreamAddNumStr(valStr);}}else{this.StreamAddNone();}}else if(propType=='str'){if(!compact||!xDef(prop.Default)||val!=prop.Default){this.StreamAddStr(val);}else{this.StreamAddNone();}}else if(propType=='arr'){var arrType=null;if(xDef(prop.ArrayType)){arrType=prop.ArrayType;}
if(!xArray(arrType))arrType=[arrType];var nTypes=arrType.length;var arrSize=val.length;var valSize=arrSize;if(xNum(prop.Size)){arrSize=prop.Size;if(arrSize<0)arrSize=0;}else{this.StreamAddInt(arrSize);}
var eleProp={Type:null};for(var i=0;i<arrSize;i++){if(i>=valSize||!arrType){this.StreamAddNone();}else{var eleType=arrType[i%nTypes];if(xObj(eleType)){if(xDef(eleType.ObjType)){this.PropToStream(val[i],eleType,compact,defPrec);}else{this.StreamAddNone();}}else{eleProp.Type=eleType;if(xDef(prop.Default)){if(xArray(prop.Default)){eleProp.Default=prop.Default[i%prop.Default.length];}else{eleProp.Default=prop.Default;}}
this.PropToStream(val[i],eleProp,compact,defPrec);}}}}else if(propType=='obj'){var props=prop.ObjType;var nProps=props.length;if(xNum(prop.DefaultPrec))defPrec=prop.DefaultPrec;for(var i=0;i<nProps;i++){var prop=props[i];this.PropToStream(val[prop.Name],prop,compact,defPrec);}}},StreamToObj:function(obj,props){var nProps=props.length;for(var i=0;i<nProps;i++){this.StreamPropToObj(obj,props[i]);}},StreamPropToObj:function(obj,prop){var propName=prop.Name;var propType=prop.Type;if(propType=='bool'){if(xBool(prop.Default))obj[propName]=prop.Default;if(!this.StreamIsNone()){obj[propName]=this.StreamGetInt()>0?true:false;}}else if(propType=='int'||propType=='int,null'){if(this.StreamIsNone()){if(xNum(prop.Default)){var defInt=Math.floor(prop.Default);if(defInt<0)defInt+=1;obj[propName]=defInt;}else{obj[propName]=null;}}else{obj[propName]=this.StreamGetInt();}}else if(propType=='num'||propType=='num,null'){if(this.StreamIsNone()){if(xNum(prop.Default)){obj[propName]=prop.Default;}else{obj[propName]=null;}}else{obj[propName]=this.StreamGetNum();}}else if(propType=='str'){if(this.StreamIsNone()){if(xStr(prop.Default))obj[propName]=prop.Default;}else{obj[propName]=this.StreamGetStr();}}else if(propType=='arr'){var arrType=null;var nTypes=0;if(xDef(prop.ArrayType)){arrType=prop.ArrayType;if(!xArray(arrType))arrType=[arrType];nTypes=arrType.length;}
var objArr=obj[propName];var arrSize;if(xNum(prop.Size)){arrSize=prop.Size;if(arrSize<0)arrSize=0;}else{arrSize=this.StreamGetInt();objArr.length=0;}
var arrProp={Name:null,Type:null};for(var i=0;i<arrSize;i++){if(arrType){var eleType=arrType[i%nTypes];if(xObj(eleType)){if(xDef(eleType.ObjType)){if(i>=objArr.length||!xDef(objArr[i])){if(xFunc(eleType.Create)){objArr[i]=eleType.Create();}else{objArr[i]={};}}
this.StreamToObj(objArr[i],eleType.ObjType);if(xFunc(eleType.Init)){eleType.Init(objArr[i]);}}}else{arrProp.Name=i;arrProp.Type=eleType;if(xDef(prop.Default)){if(xArray(prop.Default)){arrProp.Default=prop.Default[i%prop.Default.length];}else{arrProp.Default=prop.Default;}}
this.StreamPropToObj(obj[propName],arrProp);}}else{this.StreamIsNone();}}}else if(propType=='obj'){if(xDef(prop.ObjType)){this.StreamToObj(obj[propName],prop.ObjType);}}},StreamStr:'',StreamPos:0,StreamSep:'~',ResetStream:function(s){this.StreamStr=xStr(s)?s:'';this.StreamPos=0;return this;},StreamAddNone:function(){this.StreamStr+=this.StreamSep;return this;},StreamAddInt:function(i){this.StreamStr+=i+this.StreamSep;return this;},StreamAddNum:function(num){this.StreamStr+=this.FormatNum(num,this.DefaultPrec)+this.StreamSep;return this;},StreamAddNumStr:function(numStr){this.StreamStr+=numStr+this.StreamSep;return this;},StreamAddStr:function(s){this.StreamAddInt(s.length);this.StreamStr+=s+this.StreamSep;return this;},StreamIsNone:function(){if(this.StreamPos>=this.StreamStr.length)return true;if(this.StreamStr[this.StreamPos]!=this.StreamSep)return false;this.StreamPos++;return true;},StreamGetInt:function(def){def=xDefNum(def,0);if(this.StreamPos>=this.StreamStr.length)return def;var p=this.StreamStr.indexOf(this.StreamSep,this.StreamPos);if(p<0)return def;var s=this.StreamStr.slice(this.StreamPos,p);this.StreamPos=p+1;if(s=='')return def;var i=parseInt(s);if(isNaN(i))return def;return i;},StreamGetNum:function(def){def=xDefNum(def,0);if(this.StreamPos>=this.StreamStr.length)return def;var p=this.StreamStr.indexOf(this.StreamSep,this.StreamPos);if(p<0)return def;var s=this.StreamStr.slice(this.StreamPos,p);this.StreamPos=p+1;if(s=='')return def;var num=parseFloat(s);if(isNaN(num))return def;return num;},StreamGetStr:function(def){def=xStr(def,'');if(this.StreamPos>=this.StreamStr.length)return def;var l=this.StreamGetInt();if(l<0)return def;var last=this.StreamPos+l;if(last>this.StreamStr.length)return def;var s=this.StreamStr.slice(this.StreamPos,last);this.StreamPos=last+1;return s;},OnSaveRestoreDomObjKeyDown:function(evnt){if(evnt.keyCode==13&&this.SaveRestoreDomObj){evnt.PreventDefault();if(this.SaveRestoreBeforeSetStateCallback){this.SaveRestoreBeforeSetStateCallback();}
this.SetAppState();if(this.SaveRestoreAfterSetStateCallback){this.SaveRestoreAfterSetStateCallback();}
return false;}
return true;}};