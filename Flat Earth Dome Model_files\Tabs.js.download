// (C) http://walter.bislins.ch/doku/tabs

var Tabs={BoxTabList:[],IsInit:false,BoxDataList:[],SelectHandlers:[],VisiChangeHandlers:[],ButtonClickHandlers:[],IsSelectHandlerActive:false,Init:function(){this.BoxTabList=[];this.CollectTabsAndBoxes();xArrForEach(this.BoxTabList,function CB_InstallTabEventHandlers_BoxTab(boxTab){this.InstallTabEventHandlers(boxTab);},this);xArrForEach(this.SelectHandlers,function CB_AddSelectHandler_BoxTab(handler){this.AddSelectHandler(handler.BoxTab,handler.Func);},this);this.SelectHandlers=[];xArrForEach(this.VisiChangeHandlers,function CB_AddVisiChangeHandler_BoxDataDomObj(handler){this.AddVisiChangeHandler(handler.BoxDataChildDomObj,handler.Func);},this);this.VisiChangeHandlers=[];xArrForEach(this.ButtonClickHandlers,function CB_AddButtonClickHandler_BoxTab(handler){this.AddButtonClickHandler(handler.BoxTab,handler.Button,handler.Func);},this);this.ButtonClickHandlers=[];this.IsInit=true;xArrForEach(this.BoxTabList,function CB_SelectDefaultTabs_BoxTab(boxTab){this.Select(boxTab);},this);},Select:function(boxTab,ix){function containesIndex(indexes,ix){if(xNum(indexes))return(indexes==ix);var ix=xArrFindIndex(indexes,function CB_Compare_Index(index){return index==ix;});return ix>=0;}
if(!(boxTab=this.FindBoxTab(boxTab)))return;ix=xDefNum(ix,boxTab.DefaultTab);var tabDataList=boxTab.TabContainer.TabDataList;var nTabs=tabDataList.length;if(nTabs==0||ix<0||ix>=nTabs)return;for(var i=0;i<nTabs;i++){var tab=tabDataList[i];if(i==ix){xAddClass(tab.DomObj,'TabSelected');tab.IsSelected=true;}else{xRemoveClass(tab.DomObj,'TabSelected');tab.IsSelected=false;}}
boxTab.CurrentTab=ix;var boxDataList=boxTab.BoxContainer.BoxDataList;var nBoxes=boxDataList.length;for(var i=0;i<nBoxes;i++){var boxData=boxDataList[i];if(containesIndex(boxData.Indexes,ix)){xRemoveClass(boxData.DomObj,'TabHide');boxData.IsVisible=true;}else{xAddClass(boxData.DomObj,'TabHide');boxData.IsVisible=false;}}
if(this.IsSelectHandlerActive)return;this.IsSelectHandlerActive=true;try{boxTab.SelectHandlers.Call(boxTab);}catch(e){}
this.IsSelectHandlerActive=false;xArrForEach(boxTab.BoxContainer.BoxDataList,function CB_Call_VisiChangeHandler_BoxData(boxData){try{boxData.VisiChangeHandlers.Call(boxData);}catch(e){}});},ForEachBoxTab:function(func){xArrForEach(this.BoxTabList,func);},ForEachTab:function(boxTab,func){xArrForEach(boxTab.TabContainer.TabDataList,function CB_Call_Func_TabData(tabData,i){func(boxTab,i);});},AddSelectHandler:function(boxTab,func){if(!this.IsInit){this.SelectHandlers.push({BoxTab:boxTab,Func:func});return;}
if(boxTab==null){this.ForEachBoxTab(function CB_AddSelectHandler_BoxTab(boxTab){boxTab.SelectHandlers.Add(func);});return;}
if(!(boxTab=this.FindBoxTab(boxTab)))return;boxTab.SelectHandlers.Add(func);},RemoveSelectHandler:function(boxTab,func){if(!this.IsInit){xArrRemove(this.SelectHandlers,function CB_Compare_BoxTab_Func(handler){return(handler.BoxTab==boxTab&&handler.Func==func);});return;}
if(boxTab==null){this.ForEachBoxTab(function CB_RemoveSelectHandler_BoxTab(boxTab){boxTab.SelectHandlers.Remove(func);});return;}
if(!(boxTab=this.FindBoxTab(boxTab)))return;boxTab.SelectHandlers.Remove(func);},AddVisiChangeHandler:function(boxDataChildDomObj,func){if(!this.IsInit){this.VisiChangeHandlers.push({BoxDataChildDomObj:boxDataChildDomObj,Func:func});return;}
var boxData=this.FindBoxDataByChildDomObj(boxDataChildDomObj);if(boxData)boxData.VisiChangeHandlers.Add(func);},RemoveVisiChangeHandler:function(boxDataChildDomObj,func){if(!this.IsInit){xArrRemove(this.VisiChangeHandlers,function CB_Compare_BoxDataDomObj_Func(handler){return(handler.BoxDataChidDomObj==boxDataChildDomObj&&handler.Func==func);});return;}
var boxData=this.FindBoxDataByChildDomObj(boxDataChildDomObj);if(boxData)boxData.VisiChangeHandlers.Remove(func);},FindBoxDataByChildDomObj:function(domObj){while(domObj){var lastDom=domObj;domObj=xParent(domObj);if(xHasClass(domObj,'TabBoxes')){return this.FindBoxDataByDomObj(lastDom);}}
return null;},FindBoxDataByDomObj:function(domObj){var nBoxDatas=this.BoxDataList.length;for(var i=0;i<nBoxDatas;i++){var boxData=this.BoxDataList[i];if(boxData.DomObj==domObj)return boxData;}
return null;},SelectNext:function(boxTab,wrap){if(!(boxTab=this.FindBoxTab(boxTab)))return;var nextIx=this.GetNextEnabled(boxTab,wrap);if(nextIx!=-1)this.Select(boxTab,nextIx);},SelectPrev:function(boxTab,wrap){if(!(boxTab=this.FindBoxTab(boxTab)))return;var nextIx=this.GetPrevEnabled(boxTab,wrap);if(nextIx!=-1)this.Select(boxTab,nextIx);},IsSelected:function(boxTab,ix){return this.GetCurrent(boxTab)==ix;},IsEnabled:function(boxTab,ix){var tabData=this.GetTabData(boxTab,ix);if(!tabData)return false;return tabData.IsEnabled;},SetEnabled:function(boxTab,ix,enabled,select){if(!xDef(boxTab)){this.ForEachBoxTab(function CB_SetEnabled_BoxTab(boxTab){Tabs.SetEnabled(boxTab,-1,enabled);});return;}
if(!(boxTab=this.FindBoxTab(boxTab)))return;ix=xDefNum(ix,-1);if(ix==-1){this.ForEachTab(boxTab,function CB_SetEnabled_BoxTab(boxTab,ix){Tabs.SetEnabled(boxTab,ix,enabled,0);});return;}
var tabData=this.GetTabData(boxTab,ix);if(!tabData)return;enabled=xDefBool(enabled,true);select=xDefNum(select,0);if(enabled){if(tabData.IsEnabled)return;xAddClass(tabData.DomObj,'TabEnabled');tabData.IsEnabled=true;tabData.BoxTab.NEnabled++;}else{if(!tabData.IsEnabled)return;xRemoveClass(tabData.DomObj,'TabEnabled');tabData.IsEnabled=false;tabData.BoxTab.NEnabled--;if(ix==boxTab.CurrentTab&&select>=0){if(select==0){this.Select(boxTab);}else{this.SelectNext(boxTab,true);}}}},ToggleEnabled:function(boxTab,ix){if(!(boxTab=this.FindBoxTab(boxTab)))return false;var enabled=!this.IsEnabled(boxTab,ix);this.SetEnabled(boxTab,ix,enabled);return enabled;},GetNEnabled:function(boxTab){return(boxTab=this.FindBoxTab(boxTab))?boxTab.NEnabled:0;},GetNextEnabled:function(boxTab,wrap){if(!(boxTab=this.FindBoxTab(boxTab))||boxTab.NEnabled==0)return-1;wrap=xDefBool(wrap,true);var currTab=boxTab.CurrentTab;var tabDataList=boxTab.TabContainer.TabDataList;if(boxTab.NEnabled==1&&tabDataList[currTab].IsEnabled)return wrap?currTab:-1;var nTabDatas=tabDataList.length;var ix=currTab+1;while(ix<nTabDatas&&!tabDataList[ix].IsEnabled)ix++;if(ix<nTabDatas)return ix;if(!wrap)return-1;ix=0;while(!tabDataList[ix].IsEnabled)ix++;return ix;},GetPrevEnabled:function(boxTab,wrap){if(!(boxTab=this.FindBoxTab(boxTab))||boxTab.NEnabled==0)return-1;wrap=xDefBool(wrap,true);var currTab=boxTab.CurrentTab;var tabDataList=boxTab.TabContainer.TabDataList;if(boxTab.NEnabled==1&&tabDataList[currTab].IsEnabled)return wrap?currTab:-1;var ix=currTab-1;while(ix>=0&&!tabDataList[ix].IsEnabled)ix--;if(ix>=0)return ix;if(!wrap)return-1;ix=tabDataList.length-1;while(!tabDataList[ix].IsEnabled)ix--;return ix;},Reset:function(boxTab){if(!xDef(boxTab)){this.ForEachBoxTab(function CB_SelectDefault_BoxTab(boxTab){Tabs.Select(boxTab);});}else{this.Select(boxTab);}},GetCurrent:function(boxTab){return(boxTab=this.FindBoxTab(boxTab))?boxTab.CurrentTab:0;},GetDefault:function(boxTab){return(boxTab=this.FindBoxTab(boxTab))?boxTab.DefaultTab:0;},GetTabDomObj:function(boxTab,ix){var tab=this.GetTabData(boxTab,ix);return tab?tab.DomObj:null;},GetTabData:function(boxTab,ix){if(!(boxTab=this.FindBoxTab(boxTab)))return null;var tabDataList=boxTab.TabContainer.TabDataList;if(ix<0||ix>=tabDataList.length)return null;return tabDataList[ix];},GetBoxDomObj:function(boxTab,ix){var box=this.GetBoxData(boxTab,ix);return box?box.DomObj:null;},GetBoxData:function(boxTab,ix){if(!(boxTab=this.FindBoxTab(boxTab)))return null;var boxDataList=boxTab.BoxContainer.BoxDataList;if(ix<0||ix>=boxDataList.length)return null;return boxDataList[ix];},GetNTabs:function(boxTab){return(boxTab=this.FindBoxTab(boxTab))?boxTab.TabContainer.TabDataList.length:0;},SetText:function(boxTab,ix,text){var tabDom;if(!(tabDom=this.GetTabDomObj(boxTab,ix)))return;xInnerHTML(tabDom,text);},GetText:function(boxTab,ix){var tabDom;if(!(tabDom=this.GetTabDomObj(boxTab,ix)))return'';return xInnerHTML(tabDom);},GetNBoxes:function(boxTab){return(boxTab=this.FindBoxTab(boxTab))?boxTab.BoxContainer.BoxDataList.length:0;},IsBoxVisible:function(boxTab,ix){var boxData=this.GetBoxData(boxTab,ix);return boxData?boxData.IsVisible:false;},FindBoxTab:function(name){if(!xStr(name))return name;var boxTab=xArrFind(this.BoxTabList,function CB_Compare_Name(boxTab){return boxTab.Name==name;});return boxTab?boxTab:null;},FindButtonData:function(boxTab,button){if(!(boxTab=this.FindBoxTab(boxTab)))return null;var nButtons=boxTab.ButtonDataList.length;if(xNum(button)){if(button<0||button>=nButtons)return null;return boxTab.ButtonDataList[button];}
var buttonData=xArrFind(boxTab.ButtonDataList,function CB_Compare_Id(buttonData){return(buttonData.Id!=''&&buttonData.Id==button);});return buttonData?buttonData:null;},IsButtonDataOk:function(buttonData){return(xObj(buttonData)&&xDef(buttonData.BoxTab));},ForEachButton:function(boxTab,func){xArrForEach(boxTab.ButtonDataList,func);},AddButtonClickHandler:function(boxTab,button,func){if(!this.IsInit){this.ButtonClickHandlers.push({BoxTab:boxTab,Button:button,Func:func});return this;}
if(boxTab==null){this.ForEachBoxTab(function CB_AddButtonClickHandler_BoxTab(boxTab){Tabs.AddButtonClickHandler(boxTab,-1,func);});return this;}
if(this.IsButtonDataOk(boxTab)){return this.AddButtonClickHandler(boxTab.BoxTab,boxTab.Index,button);}
if(!(boxTab=this.FindBoxTab(boxTab))){return this;}
if(xNum(button)&&button==-1){this.ForEachButton(boxTab,function CB_AddClickHandler_Button(buttonData){Tabs.AddButtonClickHandler(buttonData,func);});return this;}
var buttonData=this.FindButtonData(boxTab,button);if(!buttonData)return this;var handler=function CB_Call_ClickHandler_Button(evnt){if(buttonData.IsEnabled){try{func(buttonData,evnt);}catch(e){}}}
buttonData.ClickHandlers.push({Func:func,Handler:handler});xAddEvent(buttonData.DomObj,'click',handler);return this;},RemoveButtonClickHandler:function(boxTab,button,func){if(!this.IsInit){xArrRemove(this.ButtonClickHandlers,function CB_Compare_BoxTab_Button_Func(handler){return(handler.BoxTab==boxTab&&handler.Button==button&&handler.Func==func);});return this;}
if(boxTab==null){this.ForEachBoxTab(function CB_RemoveButtonClickHandler_BoxTab(boxTab){Tabs.RemoveButtonClickHandler(boxTab,-1,func);});return this;}
if(this.IsButtonDataOk(boxTab)){return this.RemoveButtonClickHandler(boxTab.BoxTab,boxTab.Index,button);}
if(!(boxTab=this.FindBoxTab(boxTab))){return this;}
if(xNum(button)&&button==-1){this.ForEachButton(boxTab,function CB_RemoveClickHandler_Button(buttonData){Tabs.RemoveButtonClickHandler(buttonData,func);});return this;}
var buttonData=this.FindButtonData(boxTab,button);if(!buttonData)return this;xArrRemove(buttonData.ClickHandlers,function CB_RemoveClickHandler_ButtonDomObj(handler){var hit=(handler.Func==func);if(hit)xRemoveEvent(buttonData.DomObj,'click',handler.Handler);return hit;});return this;},IsButtonEnabled:function(boxTab,button){var buttonData=this.FindButtonData(boxTab,button);return buttonData?buttonData.IsEnabled:false;},SetButtonEnabled:function(boxTab,button,enabled){if(!xDef(boxTab)||boxTab==null){this.ForEachBoxTab(function CB_SetEnabled_BoxTab(boxTab){Tabs.SetButtonEnabled(boxTab,-1,enabled);});return this;}
if(this.IsButtonDataOk(boxTab))return this.SetButtonEnabled(boxTab.BoxTab,boxTab.Index,button);if(!(boxTab=this.FindBoxTab(boxTab)))return this;if(xNum(button)&&button==-1){this.ForEachButton(boxTab,function CB_SetEnabled_Button(buttonData){Tabs.SetButtonEnabled(buttonData,enabled);});return this;}
var buttonData=this.FindButtonData(boxTab,button);if(!buttonData)return this;enabled=xDefBool(enabled,true);if(enabled){if(buttonData.IsEnabled)return this;xAddClass(buttonData.DomObj,'TabEnabled');buttonData.IsEnabled=true;}else{if(!buttonData.IsEnabled)return this;xRemoveClass(buttonData.DomObj,'TabEnabled');buttonData.IsEnabled=false;}
return this;},ToggleButtonEnabled:function(boxTab,button){if(this.IsButtonDataOk(boxTab))return this.ToggleButtonEnabled(boxTab.BoxTab,boxTab.Index);if(!(boxTab=this.FindBoxTab(boxTab)))return false;var enabled=!this.IsButtonEnabled(boxTab,button)
this.SetButtonEnabled(boxTab,button,enabled);return enabled;},GetButtonDomObj:function(boxTab,button){var buttonData=this.FindButtonData(boxTab,button);return buttonData?buttonData.DomObj:null;},GetButtonId:function(boxTab,button){var buttonData=this.FindButtonData(boxTab,button);return buttonData?buttonData.Id:'';},GetButtonIndex:function(boxTab,buttonId){var buttonData=this.FindButtonData(boxTab,buttonId);return buttonData?buttonData.Index:-1;},GetButtonText:function(boxTab,button){if(this.IsButtonDataOk(boxTab))return this.GetButtonText(boxTab.BoxTab,boxTab.Index);var buttonData=this.FindButtonData(boxTab,button);return buttonData?xInnerHTML(buttonData.DomObj):'';},SetButtonText:function(boxTab,button,text){if(this.IsButtonDataOk(boxTab))return this.SetButtonText(boxTab.BoxTab,boxTab.Index,button);var buttonData=this.FindButtonData(boxTab,button);if(buttonData)xInnerHTML(buttonData.DomObj,text);return this;},InstallTabEventHandlers:function(boxTab){function newClickHandlerForTab(ix,boxTab,tabsObj){return function(e){if(tabsObj.IsEnabled(boxTab,ix)){tabsObj.Select(boxTab,ix);}};}
xArrForEach(boxTab.TabContainer.TabDataList,function CB_AddClickHandler_TabDomObj(tabData,i){xAddEvent(tabData.DomObj,'click',newClickHandlerForTab(i,boxTab,this));if(tabData.IsEnabled){xAddClass(tabData.DomObj,'TabEnabled');}},this);},CollectTabsAndBoxes:function(){var tabContainerDomList=xGetByClass('TabSelectors');var nTabContainerDoms=tabContainerDomList.length;for(var i=0;i<nTabContainerDoms;i++){var boxTab=this.CreateBoxTab(tabContainerDomList[i]);if(boxTab)this.BoxTabList.push(boxTab);}},CreateBoxTab:function(tabContainerDom){var name=tabContainerDom.id;if(!name||name=='')return null;var boxContainerDom=xGet(name+'Boxes');var boxTab={Name:name,CurrentTab:0,DefaultTab:-1,NEnabled:0,SelectHandlers:new xCallbackChain(),TabContainer:{DomObj:tabContainerDom,TabDataList:[]},BoxContainer:{DomObj:boxContainerDom,BoxDataList:[]},ButtonDataList:[]};this.InitBoxDataList(boxTab);this.InitTabDataList(boxTab);this.InitButtonDataList(boxTab);return boxTab;},InitButtonDataList:function(boxTab){var tabContainer=boxTab.TabContainer;var buttonDomList=this.CollectButtonDoms(tabContainer.DomObj);var nButtonDoms=buttonDomList.length;if(nButtonDoms==0)return;for(var i=0;i<nButtonDoms;i++){var buttonDom=buttonDomList[i];var isEnabled=xHasClass(buttonDom,'TabEnabled');var id=xDefStr(buttonDom.id,'');var buttonData={BoxTab:boxTab,DomObj:buttonDom,Id:id,Index:i,IsEnabled:isEnabled,ClickHandlers:[]};boxTab.ButtonDataList.push(buttonData);}},InitTabDataList:function(boxTab){var tabContainer=boxTab.TabContainer;var tabDomList=this.CollectTabDoms(tabContainer.DomObj);var nTabDoms=tabDomList.length;if(nTabDoms==0)return;for(var i=0;i<nTabDoms;i++){var tabDom=tabDomList[i];var isDefault=xHasClass(tabDom,'TabDefault');if(boxTab.DefaultTab==-1&&isDefault)boxTab.DefaultTab=i;var disabled=xHasClass(tabDom,'TabDisabled');xRemoveClass(tabDom,'TabDisabled');if(!disabled)boxTab.NEnabled++;var tabData={BoxTab:boxTab,DomObj:tabDom,Index:i,IsSelected:false,IsEnabled:!disabled};tabContainer.TabDataList.push(tabData);}
if(boxTab.DefaultTab==-1)boxTab.DefaultTab=0;},CollectTabDoms:function(tabContainerDom){var liDomList=xGetByTag('li',tabContainerDom);var tabDomList=[];var nLiDoms=liDomList.length;for(var i=0;i<nLiDoms;i++){var liDom=liDomList[i];if(!xHasClass(liDom,'NoTab')&&!xHasClass(liDom,'TabButton'))tabDomList.push(liDom);}
return tabDomList;},CollectButtonDoms:function(tabContainerDom){var liDomList=xGetByTag('li',tabContainerDom);var buttonDomList=[];var nLiDoms=liDomList.length;for(var i=0;i<nLiDoms;i++){var liDom=liDomList[i];if(xHasClass(liDom,'TabButton'))buttonDomList.push(liDom);}
return buttonDomList;},InitBoxDataList:function(boxTab){function getTabIndexes(indexStr){var ixStrList=indexStr.split(',');var nIxStr=ixStrList.length;var indexList=[];for(var i=0;i<nIxStr;i++){indexList.push(parseInt(ixStrList[i]));}
return indexList;}
var boxContainer=boxTab.BoxContainer;if(!boxContainer.DomObj)return;var boxDomList=this.CollectBoxDoms(boxContainer.DomObj);var nBoxDoms=boxDomList.length;if(nBoxDoms==0)return false;for(var i=0;i<nBoxDoms;i++){var boxDom=boxDomList[i];var tabixDataset=xDataset(boxDom,'tabix');var indexes=tabixDataset?getTabIndexes(tabixDataset):i;var boxData={BoxTab:boxTab,DomObj:boxDom,Indexes:indexes,IsVisible:false,VisiChangeHandlers:new xCallbackChain()};boxContainer.BoxDataList.push(boxData);this.BoxDataList.push(boxData);}},CollectBoxDoms:function(boxContainerDom){var divDomList=xGetByTag('div',boxContainerDom);var boxDomList=[];var nDivDoms=divDomList.length;for(var i=0;i<nDivDoms;i++){var divDom=divDomList[i];if((xParent(divDom)==boxContainerDom)&&!xHasClass(divDom,'NoTabBox'))boxDomList.push(divDom);}
return boxDomList;},};xOnDomReady(function CB_Init_Tabs(){Tabs.Init();});