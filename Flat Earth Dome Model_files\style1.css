
table.ControlPanel {
  width: 100%;
  font-size: 15px;
  line-height: 18px;
}
table.ControlPanel th {
  padding: 4px 8px 4px 8px;
  vertical-align: top;
  line-height: 18px;
}
table.ControlPanel td {
  padding: 4px 8px 4px 8px;
  vertical-align: top;
}
table.ControlPanel td.Value {
  white-space: nowrap;
  text-align: left;
}
table.ControlPanel td.Label {
  padding-right: 0px;
  text-align: right;
  white-space: nowrap;
  font-weight: bold;
}
table.ControlPanel input[type="text"] {
  margin: 1px 3px 3px 0px;
  padding: 2px 3px 1px 3px;
  font-size: 14px;
  line-height: 16px;
  font-weight: normal;
  vertical-align: top;
}
table.ControlPanel input[type="text"].ReadOnly {
  margin: 2px 3px 4px 0px;
  padding: 2px 3px 1px 3px;
  border: 1px solid #ddd;
  vertical-align: top;
}
table.ControlPanel input[type="radio"] {
  margin: 0px 4px 2px 0px;
  vertical-align: bottom;
  background-color: transparent;
}
table.ControlPanel input[type="checkbox"] {
  margin: 0px 4px 2px 0px;
  vertical-align: bottom;
  background-color: transparent;
}
table.ControlPanel td.Value input[type="text"] {
  text-align:right;
  width:150px;
}
table.ControlPanel div.FieldText {
  display:inline-block;
  padding-top: 4px;
  padding-bottom: 5px;
  vertical-align: top;
  line-height: normal;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  cursor: default;
}
table.ControlPanel table.FieldGrid {
  display: inline-block;
  border-collapse: separate;
  border-spacing: 1px;
}
table.ControlPanel table.FieldGrid td.FieldCell {
  border: 1px solid #fff;
  padding-left: 4px;
}
table.ControlPanel table.FieldGrid td.FieldCell:hover {
  background-color: #eee;
  border: 1px solid #ddd;
}
table.ControlPanel table.FieldGrid td.FieldCell.Disabled {
  color: #ccc;
  background-color: transparent;
  border: 1px solid #fff;
  cursor: default;
}
table.ControlPanel table.FieldGrid td.FieldCell.ReadOnly {
  background-color: transparent;
  border: 1px solid #fff;
  cursor: default;
}
table.ControlPanel .FieldCell, table.ControlPanel .FieldCaption {
  cursor: pointer; cursor: hand;
}
table.ControlPanel .Disabled .FieldCaption {
  cursor: default;
}
table.ControlPanel .FieldCell input {
  cursor: default;
}
table.ControlPanel img.HelpImg {
  margin-top: 6px;
  vertical-align: top;
}
table.ControlPanel div.HtmlField {
  margin:0;
  padding:0;
  border:none;
  white-space:normal;
  padding-top: 4px;
}
table.ControlPanel.InputLeft td.Value input[type="text"] { text-align:Left; }
table.ControlPanel.LabelLeft td.Label { text-align: left; }
table.ControlPanel.MathLabels td.Label { font-family:'Times New Roman', Times, serif; font-style:italic; }
table.ControlPanel.WideFieldGrid table.FieldGrid { width:100%; }
table.ControlPanel.InputMiniWidth td.Value input[type="text"] { width: 35px !important; }
table.ControlPanel.InputShortWidth td.Value input[type="text"] { width: 65px !important; }
table.ControlPanel.InputMediumWidth td.Value input[type="text"] { width: 100px !important; }
table.ControlPanel.InputSmallerWidth td.Value input[type="text"] { width: 120px !important; }
table.ControlPanel.InputNormalWidth td.Value input[type="text"] { width: 150px !important; }
table.ControlPanel.InputBigWidth td.Value input[type="text"] { width: 160px !important; }
table.ControlPanel.InputLongWidth td.Value input[type="text"] { width: 200px !important; }
table.ControlPanel.InputMaxWidth td.Value input[type="text"] { width: 100% !important; }
table.ControlPanel.InputWidth35 td.Value input[type="text"] { width: 35px !important; }
table.ControlPanel.InputWidth45 td.Value input[type="text"] { width: 45px !important; }
table.ControlPanel.InputWidth55 td.Value input[type="text"] { width: 55px !important; }
table.ControlPanel.InputWidth65 td.Value input[type="text"] { width: 65px !important; }
table.ControlPanel.InputWidth75 td.Value input[type="text"] { width: 75px !important; }
table.ControlPanel.InputWidth185 td.Value input[type="text"] { width: 185px !important; }
table.ControlPanel.SliderMinWidth250 td.Slider { min-width: 250px !important; }
table.ControlPanel.SliderMinWidth300 td.Slider { min-width: 300px !important; }
table.ControlPanel.HtmlFieldPadding0 div.HtmlField { padding: 0 !important; }
table.ControlPanel td.Label { width:10%; }
table.ControlPanel.Slider td.Label { width:1em; }
table.ControlPanel.Slider td.Value input[type="text"] { width:50px; }
table.ControlPanel.Slider.NCols2 td.Slider { width:99%; min-width:200px; }
table.ControlPanel.Slider.NCols4 td.Slider { width:48%; min-width:200px; }
table.ControlPanel.Slider.Left td.SliderValue { padding-left:0; }
table.ControlPanel.Slider.Right td.SliderValue { padding-right:0; }
div.Slider {
  position: relative;
  height: 25px;
  background: #EEE;
  border:1px solid #ddd;
  touch-action: none;
}
div.Slider .Handle {
  position: absolute;
  width: 50px;
  height: 25px;
  background: #CC0000;
  color: #FFF;
  line-height: 22px;
  text-align: center;
  cursor: pointer;
  -webkit-border-radius: 3px;
  -moz-border-radius: 3px;
  border-radius: 3px;
  touch-action: none;
  -webkit-touch-callout: none;
  -webkit-user-select: none;
  -khtml-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}
div.Slider .Handle.Disabled {
  background: #DDD !important;
  color: #FFF;
  cursor: default;
}
div.Slider.ReadOnly {
  background: #FFF;
  cursor: default;
}
div.Slider.ReadOnly .Handle {
  cursor: default;
}
.TabBoxes > div { display:block; }
.TabBoxes > div.TabHide { display:none; }
ul.TabSelectors li.TabHide { display:none; }
ul.TabSelectors li.TabActive { border-color: #000!important; }
ul.TabSelectors li.TabButton { border-radius: 4px; }
ul.TabSelectors {
  list-style: none;
  margin: 0;
  padding: 3px 0;
  font-weight: bold;
  text-align: left;
}
ul.TabSelectors.Top {
  padding-bottom: 0;
}
ul.TabSelectors.Bottom {
  padding-top: 0;
}
ul.TabSelectors.Top.Line {
  border-bottom: 1px solid #ddd;
}
ul.TabSelectors.Bottom.Line {
  border-top: 1px solid #ddd;
}
ul.TabSelectors li {
  background: none;
  display: inline;
  margin: 0 0.2em 0 0.2em;
  padding: 0.2em 0.5em 0.2em 0.5em;
  line-height: 2;
  border-left: 1px solid #ddd;
  color: #ddd;
  white-space: nowrap;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  cursor: default;
}
ul.TabSelectors.Top li {
  border-top: 4px solid #ddd;
}
ul.TabSelectors.Bottom li {
  border-bottom: 4px solid #ddd;
}
ul.TabSelectors li.NoTab {
  color: #000;
  border: none;
  padding-left: 0;
  padding-right: 0;
  margin-left: 0;
}
ul.TabSelectors li.TabEnabled {
  color: #666;
  cursor: pointer; cursor: hand;
}
ul.TabSelectors li.TabEnabled:hover {
  border-color: #666;
  color:#000;
}
ul.TabSelectors li.TabSelected {
  border-color: #ff8033;
}
ul.TabSelectors li.TabSelected:hover {
}
ul.TabSelectors li.TabEnabled.TabSelected:hover {
  border-color: #ff8033;
  color: #666;
}
ul.TabSelectors.Top li.TabButton {
  border-bottom: 1px solid #ddd;
}
ul.TabSelectors.Bottom li.TabButton {
  border-top: 1px solid #ddd;
}
ul.TabSelectors li.TabButton {
  border-right: 1px solid #ddd;
  color: #ddd;
}
ul.TabSelectors li.TabButton.TabEnabled {
  border-color: #ff8033;
  color: #666;
}
ul.TabSelectors li.TabButton.TabEnabled:hover {
  color: #000;
}
.Wiki .ControlPanel { margin: 0; }
textarea.ListingDisplay {
  width: 100%;
  font-family: Courier;
  margin-bottom: 1em;
}
#ResetButton { }
#BackButton { margin-left: 1.2em; }

.MathJax_Hover_Frame {border-radius: .25em; -webkit-border-radius: .25em; -moz-border-radius: .25em; -khtml-border-radius: .25em; box-shadow: 0px 0px 15px #83A; -webkit-box-shadow: 0px 0px 15px #83A; -moz-box-shadow: 0px 0px 15px #83A; -khtml-box-shadow: 0px 0px 15px #83A; border: 1px solid #A6D ! important; display: inline-block; position: absolute}
.MathJax_Menu_Button .MathJax_Hover_Arrow {position: absolute; cursor: pointer; display: inline-block; border: 2px solid #AAA; border-radius: 4px; -webkit-border-radius: 4px; -moz-border-radius: 4px; -khtml-border-radius: 4px; font-family: 'Courier New',Courier; font-size: 9px; color: #F0F0F0}
.MathJax_Menu_Button .MathJax_Hover_Arrow span {display: block; background-color: #AAA; border: 1px solid; border-radius: 3px; line-height: 0; padding: 4px}
.MathJax_Hover_Arrow:hover {color: white!important; border: 2px solid #CCC!important}
.MathJax_Hover_Arrow:hover span {background-color: #CCC!important}

#MathJax_About {position: fixed; left: 50%; width: auto; text-align: center; border: 3px outset; padding: 1em 2em; background-color: #DDDDDD; color: black; cursor: default; font-family: message-box; font-size: 120%; font-style: normal; text-indent: 0; text-transform: none; line-height: normal; letter-spacing: normal; word-spacing: normal; word-wrap: normal; white-space: nowrap; float: none; z-index: 201; border-radius: 15px; -webkit-border-radius: 15px; -moz-border-radius: 15px; -khtml-border-radius: 15px; box-shadow: 0px 10px 20px #808080; -webkit-box-shadow: 0px 10px 20px #808080; -moz-box-shadow: 0px 10px 20px #808080; -khtml-box-shadow: 0px 10px 20px #808080; filter: progid:DXImageTransform.Microsoft.dropshadow(OffX=2, OffY=2, Color='gray', Positive='true')}
#MathJax_About.MathJax_MousePost {outline: none}
.MathJax_Menu {position: absolute; background-color: white; color: black; width: auto; padding: 2px; border: 1px solid #CCCCCC; margin: 0; cursor: default; font: menu; text-align: left; text-indent: 0; text-transform: none; line-height: normal; letter-spacing: normal; word-spacing: normal; word-wrap: normal; white-space: nowrap; float: none; z-index: 201; box-shadow: 0px 10px 20px #808080; -webkit-box-shadow: 0px 10px 20px #808080; -moz-box-shadow: 0px 10px 20px #808080; -khtml-box-shadow: 0px 10px 20px #808080; filter: progid:DXImageTransform.Microsoft.dropshadow(OffX=2, OffY=2, Color='gray', Positive='true')}
.MathJax_MenuItem {padding: 2px 2em; background: transparent}
.MathJax_MenuArrow {position: absolute; right: .5em; padding-top: .25em; color: #666666; font-size: .75em}
.MathJax_MenuActive .MathJax_MenuArrow {color: white}
.MathJax_MenuArrow.RTL {left: .5em; right: auto}
.MathJax_MenuCheck {position: absolute; left: .7em}
.MathJax_MenuCheck.RTL {right: .7em; left: auto}
.MathJax_MenuRadioCheck {position: absolute; left: 1em}
.MathJax_MenuRadioCheck.RTL {right: 1em; left: auto}
.MathJax_MenuLabel {padding: 2px 2em 4px 1.33em; font-style: italic}
.MathJax_MenuRule {border-top: 1px solid #CCCCCC; margin: 4px 1px 0px}
.MathJax_MenuDisabled {color: GrayText}
.MathJax_MenuActive {background-color: Highlight; color: HighlightText}
.MathJax_MenuDisabled:focus, .MathJax_MenuLabel:focus {background-color: #E8E8E8}
.MathJax_ContextMenu:focus {outline: none}
.MathJax_ContextMenu .MathJax_MenuItem:focus {outline: none}
#MathJax_AboutClose {top: .2em; right: .2em}
.MathJax_Menu .MathJax_MenuClose {top: -10px; left: -10px}
.MathJax_MenuClose {position: absolute; cursor: pointer; display: inline-block; border: 2px solid #AAA; border-radius: 18px; -webkit-border-radius: 18px; -moz-border-radius: 18px; -khtml-border-radius: 18px; font-family: 'Courier New',Courier; font-size: 24px; color: #F0F0F0}
.MathJax_MenuClose span {display: block; background-color: #AAA; border: 1.5px solid; border-radius: 18px; -webkit-border-radius: 18px; -moz-border-radius: 18px; -khtml-border-radius: 18px; line-height: 0; padding: 8px 0 6px}
.MathJax_MenuClose:hover {color: white!important; border: 2px solid #CCC!important}
.MathJax_MenuClose:hover span {background-color: #CCC!important}
.MathJax_MenuClose:hover:focus {outline: none}

.MathJax_Preview .MJXf-math {color: inherit!important}

.MJX_Assistive_MathML {position: absolute!important; top: 0; left: 0; clip: rect(1px, 1px, 1px, 1px); padding: 1px 0 0 0!important; border: 0!important; height: 1px!important; width: 1px!important; overflow: hidden!important; display: block!important; -webkit-touch-callout: none; -webkit-user-select: none; -khtml-user-select: none; -moz-user-select: none; -ms-user-select: none; user-select: none}
.MJX_Assistive_MathML.MJX_Assistive_MathML_Block {width: 100%!important}

#MathJax_Zoom {position: absolute; background-color: #F0F0F0; overflow: auto; display: block; z-index: 301; padding: .5em; border: 1px solid black; margin: 0; font-weight: normal; font-style: normal; text-align: left; text-indent: 0; text-transform: none; line-height: normal; letter-spacing: normal; word-spacing: normal; word-wrap: normal; white-space: nowrap; float: none; -webkit-box-sizing: content-box; -moz-box-sizing: content-box; box-sizing: content-box; box-shadow: 5px 5px 15px #AAAAAA; -webkit-box-shadow: 5px 5px 15px #AAAAAA; -moz-box-shadow: 5px 5px 15px #AAAAAA; -khtml-box-shadow: 5px 5px 15px #AAAAAA; filter: progid:DXImageTransform.Microsoft.dropshadow(OffX=2, OffY=2, Color='gray', Positive='true')}
#MathJax_ZoomOverlay {position: absolute; left: 0; top: 0; z-index: 300; display: inline-block; width: 100%; height: 100%; border: 0; padding: 0; margin: 0; background-color: white; opacity: 0; filter: alpha(opacity=0)}
#MathJax_ZoomFrame {position: relative; display: inline-block; height: 0; width: 0}
#MathJax_ZoomEventTrap {position: absolute; left: 0; top: 0; z-index: 302; display: inline-block; border: 0; padding: 0; margin: 0; background-color: white; opacity: 0; filter: alpha(opacity=0)}

.MathJax_Preview {color: #888}
#MathJax_Message {position: fixed; left: 1em; bottom: 1.5em; background-color: #E6E6E6; border: 1px solid #959595; margin: 0px; padding: 2px 8px; z-index: 102; color: black; font-size: 80%; width: auto; white-space: nowrap}
#MathJax_MSIE_Frame {position: absolute; top: 0; left: 0; width: 0px; z-index: 101; border: 0px; margin: 0px; padding: 0px}
.MathJax_Error {color: #CC0000; font-style: italic}

.MJXp-script {font-size: .8em}
.MJXp-right {-webkit-transform-origin: right; -moz-transform-origin: right; -ms-transform-origin: right; -o-transform-origin: right; transform-origin: right}
.MJXp-bold {font-weight: bold}
.MJXp-italic {font-style: italic}
.MJXp-scr {font-family: MathJax_Script,'Times New Roman',Times,STIXGeneral,serif}
.MJXp-frak {font-family: MathJax_Fraktur,'Times New Roman',Times,STIXGeneral,serif}
.MJXp-sf {font-family: MathJax_SansSerif,'Times New Roman',Times,STIXGeneral,serif}
.MJXp-cal {font-family: MathJax_Caligraphic,'Times New Roman',Times,STIXGeneral,serif}
.MJXp-mono {font-family: MathJax_Typewriter,'Times New Roman',Times,STIXGeneral,serif}
.MJXp-largeop {font-size: 150%}
.MJXp-largeop.MJXp-int {vertical-align: -.2em}
.MJXp-math {display: inline-block; line-height: 1.2; text-indent: 0; font-family: 'Times New Roman',Times,STIXGeneral,serif; white-space: nowrap; border-collapse: collapse}
.MJXp-display {display: block; text-align: center; margin: 1em 0}
.MJXp-math span {display: inline-block}
.MJXp-box {display: block!important; text-align: center}
.MJXp-box:after {content: " "}
.MJXp-rule {display: block!important; margin-top: .1em}
.MJXp-char {display: block!important}
.MJXp-mo {margin: 0 .15em}
.MJXp-mfrac {margin: 0 .125em; vertical-align: .25em}
.MJXp-denom {display: inline-table!important; width: 100%}
.MJXp-denom > * {display: table-row!important}
.MJXp-surd {vertical-align: top}
.MJXp-surd > * {display: block!important}
.MJXp-script-box > *  {display: table!important; height: 50%}
.MJXp-script-box > * > * {display: table-cell!important; vertical-align: top}
.MJXp-script-box > *:last-child > * {vertical-align: bottom}
.MJXp-script-box > * > * > * {display: block!important}
.MJXp-mphantom {visibility: hidden}
.MJXp-munderover, .MJXp-munder {display: inline-table!important}
.MJXp-over {display: inline-block!important; text-align: center}
.MJXp-over > * {display: block!important}
.MJXp-munderover > *, .MJXp-munder > * {display: table-row!important}
.MJXp-mtable {vertical-align: .25em; margin: 0 .125em}
.MJXp-mtable > * {display: inline-table!important; vertical-align: middle}
.MJXp-mtr {display: table-row!important}
.MJXp-mtd {display: table-cell!important; text-align: center; padding: .5em 0 0 .5em}
.MJXp-mtr > .MJXp-mtd:first-child {padding-left: 0}
.MJXp-mtr:first-child > .MJXp-mtd {padding-top: 0}
.MJXp-mlabeledtr {display: table-row!important}
.MJXp-mlabeledtr > .MJXp-mtd:first-child {padding-left: 0}
.MJXp-mlabeledtr:first-child > .MJXp-mtd {padding-top: 0}
.MJXp-merror {background-color: #FFFF88; color: #CC0000; border: 1px solid #CC0000; padding: 1px 3px; font-style: normal; font-size: 90%}
.MJXp-scale0 {-webkit-transform: scaleX(.0); -moz-transform: scaleX(.0); -ms-transform: scaleX(.0); -o-transform: scaleX(.0); transform: scaleX(.0)}
.MJXp-scale1 {-webkit-transform: scaleX(.1); -moz-transform: scaleX(.1); -ms-transform: scaleX(.1); -o-transform: scaleX(.1); transform: scaleX(.1)}
.MJXp-scale2 {-webkit-transform: scaleX(.2); -moz-transform: scaleX(.2); -ms-transform: scaleX(.2); -o-transform: scaleX(.2); transform: scaleX(.2)}
.MJXp-scale3 {-webkit-transform: scaleX(.3); -moz-transform: scaleX(.3); -ms-transform: scaleX(.3); -o-transform: scaleX(.3); transform: scaleX(.3)}
.MJXp-scale4 {-webkit-transform: scaleX(.4); -moz-transform: scaleX(.4); -ms-transform: scaleX(.4); -o-transform: scaleX(.4); transform: scaleX(.4)}
.MJXp-scale5 {-webkit-transform: scaleX(.5); -moz-transform: scaleX(.5); -ms-transform: scaleX(.5); -o-transform: scaleX(.5); transform: scaleX(.5)}
.MJXp-scale6 {-webkit-transform: scaleX(.6); -moz-transform: scaleX(.6); -ms-transform: scaleX(.6); -o-transform: scaleX(.6); transform: scaleX(.6)}
.MJXp-scale7 {-webkit-transform: scaleX(.7); -moz-transform: scaleX(.7); -ms-transform: scaleX(.7); -o-transform: scaleX(.7); transform: scaleX(.7)}
.MJXp-scale8 {-webkit-transform: scaleX(.8); -moz-transform: scaleX(.8); -ms-transform: scaleX(.8); -o-transform: scaleX(.8); transform: scaleX(.8)}
.MJXp-scale9 {-webkit-transform: scaleX(.9); -moz-transform: scaleX(.9); -ms-transform: scaleX(.9); -o-transform: scaleX(.9); transform: scaleX(.9)}
.MathJax_PHTML .noError {vertical-align: ; font-size: 90%; text-align: left; color: black; padding: 1px 3px; border: 1px solid}

.MathJax_SVG_Display {text-align: center; margin: 1em 0em; position: relative; display: block!important; text-indent: 0; max-width: none; max-height: none; min-width: 0; min-height: 0; width: 100%}
.MathJax_SVG .MJX-monospace {font-family: monospace}
.MathJax_SVG .MJX-sans-serif {font-family: sans-serif}
#MathJax_SVG_Tooltip {background-color: InfoBackground; color: InfoText; border: 1px solid black; box-shadow: 2px 2px 5px #AAAAAA; -webkit-box-shadow: 2px 2px 5px #AAAAAA; -moz-box-shadow: 2px 2px 5px #AAAAAA; -khtml-box-shadow: 2px 2px 5px #AAAAAA; padding: 3px 4px; z-index: 401; position: absolute; left: 0; top: 0; width: auto; height: auto; display: none}
.MathJax_SVG {display: inline; font-style: normal; font-weight: normal; line-height: normal; font-size: 100%; font-size-adjust: none; text-indent: 0; text-align: left; text-transform: none; letter-spacing: normal; word-spacing: normal; word-wrap: normal; white-space: nowrap; float: none; direction: ltr; max-width: none; max-height: none; min-width: 0; min-height: 0; border: 0; padding: 0; margin: 0}
.MathJax_SVG * {transition: none; -webkit-transition: none; -moz-transition: none; -ms-transition: none; -o-transition: none}
.MathJax_SVG > div {display: inline-block}
.mjx-svg-href {fill: blue; stroke: blue}
.MathJax_SVG_Processing {visibility: hidden; position: absolute; top: 0; left: 0; width: 0; height: 0; overflow: hidden; display: block!important}
.MathJax_SVG_Processed {display: none!important}
.MathJax_SVG_test {font-style: normal; font-weight: normal; font-size: 100%; font-size-adjust: none; text-indent: 0; text-transform: none; letter-spacing: normal; word-spacing: normal; overflow: hidden; height: 1px}
.MathJax_SVG_test.mjx-test-display {display: table!important}
.MathJax_SVG_test.mjx-test-inline {display: inline!important; margin-right: -1px}
.MathJax_SVG_test.mjx-test-default {display: block!important; clear: both}
.MathJax_SVG_ex_box {display: inline-block!important; position: absolute; overflow: hidden; min-height: 0; max-height: none; padding: 0; border: 0; margin: 0; width: 1px; height: 60ex}
.mjx-test-inline .MathJax_SVG_left_box {display: inline-block; width: 0; float: left}
.mjx-test-inline .MathJax_SVG_right_box {display: inline-block; width: 0; float: right}
.mjx-test-display .MathJax_SVG_right_box {display: table-cell!important; width: 10000em!important; min-width: 0; max-width: none; padding: 0; border: 0; margin: 0}
.MathJax_SVG .noError {vertical-align: ; font-size: 90%; text-align: left; color: black; padding: 1px 3px; border: 1px solid}
