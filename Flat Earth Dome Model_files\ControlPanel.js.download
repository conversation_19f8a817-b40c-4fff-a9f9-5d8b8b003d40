// (C) http://walter.bislins.ch/doku/ControlPanel

var ControlPanels={Debug:false,Counter:0,PanelList:[],PanelInitList:[],BeforeInitHandlers:new xCallback<PERSON>hain(),AddBeforeInitHandler:function(func){this.BeforeInitHandlers.Add(func);},NewPanel:function(aParams){return new ControlPanel(aParams);},NewSliderPanel:function(aParams){var panel=this.NewPanel(aParams);panel.NCols=xNum(aParams.NCols)?aParams.NCols*2:2;panel.ValuePos=xDefStr(aParams.ValuePos,'left');if(panel.PanelFormat!='')panel.PanelFormat+=' ';if(panel.ValuePos=='left'){panel.PanelFormat+='Slider Right';}else{panel.PanelFormat+='Slider Left';}
return panel;},ResetButton:function(){return this.SimpleButton('Reset','Reset()','Black','Small',false,false);},ResetButtonR:function(){return this.SimpleButton('Reset','Reset()','Black','Small',true,false);},SmallButtonR:function(text,code,color){return this.SimpleButton(text,code,color,'Small',true,false);},SmallButton:function(text,code,color){return this.SimpleButton(text,code,color,'Small',false,false);},SimpleButton:function(text,code,color,size,right,margin){var params={};params.Text=xDefStr(text,'Reset');params.Code=xDefStr(code,'Reset()');params.Color=xDefStr(color,'Black');if(xStr(size))params.Size=size;if(xBool(right))params.Right=right;if(xBool(margin))params.Margin=margin;return this.Button(params);},Button:function(params){var text=xDefStr(params.Text,'Text');var code=xDefStr(params.Code,'alert("no action defined")');var color=xDefStr(params.Color,'Black');var size=xDefStr(params.Size,'');var right=xDefBool(params.Right,false);var margin=xDefBool(params.Margin,true);var cls=xDefStr(params.Class,'');var aStyle='';var aClass='';var spanClass='lbtn';var spanStyle='';if(right)aStyle=' style="float:right;"';if(cls!='')aClass=' class="'+cls+'"';if(color!='')spanClass+=' lbtn'+color;if(size!='')spanClass+=' lbtn'+size;if(!margin)spanStyle+=' style="margin-bottom:0 !important;margin-top:0 !important;"';if(spanClass!='')spanClass=' class="'+spanClass+'"';return'<a href="javascript:'+code+'"'+aClass+aStyle+'><span'+spanClass+spanStyle+'>'+text+'</span></a>';},AddToPanelList:function(panelList,panelNames){var panelNameList=panelNames.replace(/ +/g,'').split(',');var added=false;for(var j=0;j<panelNameList.length;j++){var name=panelNameList[j];var found=false;for(var i=0;i<panelList.length;i++){if(panelList[i]==name){found=true;break;}}
if(!found){panelList.push(name);added=true;}}
return added;},MatchesField:function(controller,names){if(!xDef(controller))return true;if(xStr(controller))return this.ContainsStr(names,controller);return this.ContainsStr(names,controller.ValueRef.ValueRef);},MatchesPanel:function(controller,names){if(!xDef(controller))return true;if(xStr(controller))return this.ContainsStr(names,controller);return this.ContainsStr(names,controller.Panel.Name);},MatchesNames:function(controller,names){if(!xDef(controller))return true;if(!xStr(controller))return false;return this.ContainsStr(names,controller);},MatchesAny:function(controller){return!xDef(controller);},GetPanelNames:function(controller){if(xArray(controller)){return controller;}
if(xStr(controller)){var sList=controller.split(',');return sList;}
if(xObj(controller)){return controller.Panel.Name;}
return null;},ContainsStr:function(s,find){var sList=s.split(',');var fList=find.split(',');for(var i=0;i<sList.length;i++){for(var j=0;j<fList.length;j++){if(sList[i]!=''&&fList[j]!=''&&sList[i]==fList[j]){return true;}}}
return false;},Update:function(panelRefs){this.ForEachPanel(function CB_UpdatePanel(p){p.Update();},panelRefs);},UpdateLayout:function(panelRefs){this.ForEachPanel(function CB_UpdatePanelLayout(p){p.UpdateLayout();},panelRefs);},Invalidate:function(panelRefs,updateGui){this.ForEachPanel(function CB_InvalidatePanel(p){p.Invalidate(updateGui);},panelRefs);},Refresh:function(panelRefs,updateGui){this.ForEachPanel(function CB_RefreshPanel(p){p.Refresh(updateGui);},panelRefs);},Reset:function(panelRefs,callOnModelChaned){this.ForEachPanel(function CB_ResetPanel(p){p.Reset(callOnModelChaned);},panelRefs);},ConnectDom:function(panelRefs){this.ForEachPanel(function CB_InitPanel(p){p.Init();},panelRefs);},Init:function(panelRefs,forceGetDefault){this.ForEachPanel(function CB_InitPanel(p){p.Init(forceGetDefault);},panelRefs);},SetDefaults:function(panelRefs,updateGui){this.ForEachPanel(function CB_SetDefaults(p){p.SetDefaults(updateGui);},panelRefs);},IsDisplayed:function(panelName){var panel=this.Get(panelName);return panel?panel.IsDisplayed():false;},IsEnabled:function(panelName,fieldName){var panel=this.Get(panelName);return panel?panel.IsEnabled(fieldName):true;},SetEnabled:function(panelRef,fieldName,enabled){fieldName=xDefStr(fieldName,'');enabled=xDefBool(enabled,true);this.ForEachPanel(function CB_SetPanelEnabled(p){p.SetEnabled(fieldName,enabled);},panelRef);},DeletePanels:function(panelRef,deleteDom){if(!xArray(panelRef))panelRef=this.PanelList.slice();this.ForEachPanel(function CB_DeletePanel(p){p.Delete(deleteDom);},panelRef);},RemovePanel:function(panel){xArrRemove(this.PanelInitList,function CB_Compare_Panel(p){return p==panel;});xArrRemove(this.PanelList,function CB_Compare_Panel(p){return p==panel;});},AddPanel:function(panel,autoInit){autoInit=xDefBool(autoInit,true);this.PanelList.push(panel);if(xOnLoadFinished||!autoInit)return false;this.PanelInitList.push(panel);if(this.PanelInitList.length==1){xOnDomReady(function CB_OnLoad_InitPanels(){ControlPanels.BeforeInitHandlers.Call();ControlPanels.ForEachPanel(function CB_InitPanel(p){p.Init();},null,ControlPanels.PanelInitList);ControlPanels.PanelInitList=[];});}
return true;},Get:function(name,panelList){panelList=xDefArray(panelList,this.PanelList);var panel=xArrFind(panelList,function CB_Compare_Name(p){return p.Name==name;});return panel?panel:null;},GetIx:function(name,panelList){panelList=xDefArray(panelList,this.PanelList);return xArrFindIndex(panelList,function CB_Compare_Name(p){return p.Name==name;});},GetField:function(panelName,fieldName){var panel=this.Get(panelName);return panel?panel.GetField(fieldName):null;},ForEachPanel:function(func,panelRefs,panelList){panelList=xDefArray(panelList,this.PanelList);if(!xAny(panelRefs)){xArrForEach(panelList,func);}else if(xArray(panelRefs)){xArrForEach(panelRefs,function CB_Call_Func(panel){if(xStr(panel))panel=this.Get(panel,panelList);if(panel)func(panel);},this);}else{if(xStr(panelRefs))panelRefs=this.Get(panelRefs,panelList);if(panelRefs)func(panelRefs);}},};function ControlPanel(aParams){ControlPanels.Counter++;aParams=xDefObj(aParams,{});this.Name=xDefStr(aParams.Name,'ControlPanel'+ControlPanels.Counter);this.DomObj=null;this.ModelRef=xDefStr(aParams.ModelRef,'');this.NCols=xDefNum(aParams.NCols,1);this.Attr=xDefStr(aParams.Attr,'');this.Headers=[];this.HeaderAttrs=[];this.Fields=[];this.Format=xDefStr(aParams.Format,'fix');this.FormatTab=xDefBool(aParams.FormatTab,true);this.Digits=xDefNum(aParams.Digits,2);this.ReadOnly=xDefBool(aParams.ReadOnly,false);this.Enabled=xDefBool(aParams.Enabled,true);this.EnabledRef=null;if(xFunc(aParams.EnabledRef)){this.EnabledRef=aParams.EnabledRef;}else{this.EnabledRef=this.MakeRef(aParams.EnabledRef,'');}
this.PanelFormat=xDefStr(aParams.PanelFormat,'');this.PanelClass=xDefStr(aParams.PanelClass,'ControlPanel');this.LabelClass=xDefStr(aParams.LabelClass,'Label');this.ValueClass=xDefStr(aParams.ValueClass,'Value');this.FieldClass=xDefStr(aParams.FieldClass,'Field');this.HelpImage=xDefStr(aParams.HelpImage,'q.gif');this.OnModelChange=xDefFunc(aParams.OnModelChange,null);this.ModelChangeActive=false;this.FieldCounter=0;this.FieldUpdateLayoutFuncCounter=0;this.LayoutHasChanged=false;this.IsInit=false;this.VisiChangeHandler=null;this.LayoutChangeHandler=null;ControlPanels.AddPanel(this,aParams.AutoInit);}
ControlPanel.prototype.SetLayoutHasChanged=function(){this.LayoutHasChanged=true;}
ControlPanel.prototype.Delete=function(deleteDom){if(this.VisiChangeHandler){Tabs.RemoveVisiChangeHandler(this.DomObj,this.VisiChangeHandler);this.VisiChangeHandler=null;}
if(this.LayoutChangeHandler){xRemoveEventLayoutChange(this.LayoutChangeHandler);this.LayoutChangeHandler=null;}
this.ForEachField(function CB_FreeField(field){field.Free();});this.Fields=[];deleteDom=xDefBool(deleteDom,false);if(deleteDom){var domObj=this.GetDomObj();if(domObj)xRemoveChild(xParent(domObj),domObj);}
this.DomObj=null;ControlPanels.RemovePanel(this);this.IsInit=false;}
ControlPanel.prototype.ScriptPath=(function(){var scripts=document.getElementsByTagName('script'),script=scripts[scripts.length-1];var path='';if(script.getAttribute.length!==undefined){path=script.getAttribute('src');}else{path=script.getAttribute('src',2);}
if(path){path=path.substr(0,path.lastIndexOf('/')+1);}
return path;}());ControlPanel.prototype.GetHtmlID=function(){return this.Name;}
ControlPanel.prototype.GetDomObj=function(){return this.DomObj?this.DomObj:xGet(this.GetHtmlID());}
ControlPanel.prototype.MakeFieldName=function(aFieldName){this.FieldCounter++;return xDefStr(aFieldName,'Field'+this.FieldCounter);}
ControlPanel.prototype.MakeFieldHtmlID=function(aFieldName){return this.Name+'-'+aFieldName;}
ControlPanel.prototype.MakeRef=function(aValueRef,aName){var ref=xDefStr(aValueRef,aName);if(ref==='')return null;var refObj={ValueRef:ref};if(ref.indexOf('.')==-1&&this.ModelRef){ref=this.ModelRef+'.'+ref;}
var refx=ref.replace(/\[([^\]]+)\]/g,'.$1');var modelRef=window;var refParts=refx.split('.');var last=refParts.length-1;for(var i=0;i<last;i++){modelRef=modelRef[refParts[i]];}
refObj.RefStr=ref;refObj.ModelRef=modelRef;refObj.PropRef=refParts[last];return refObj;}
ControlPanel.prototype.GetField=function(aFieldOrItemName){var field=xArrFind(this.Fields,function CB_HasFieldName(f){return f.HasName(aFieldOrItemName);});return field?field:null;}
ControlPanel.prototype.ForEachField=function(func){xArrForEach(this.Fields,func,this);}
ControlPanel.prototype.IsEnabled=function(aFieldOrItemName){var field=this.GetField(aFieldOrItemName);return field?field.IsEnabled(aFieldOrItemName):false;}
ControlPanel.prototype.SetEnabled=function(aFieldOrItemName,enabled){if(!this.IsInit)return;enabled=xDefBool(enabled,true);if(!xDef(aFieldOrItemName)||aFieldOrItemName==null)aFieldOrItemName='';if(aFieldOrItemName==''){this.ForEachField(function CB_SetFieldEnabled(field){field.SetEnabled(aFieldOrItemName,enabled);});}else{var field=this.GetField(aFieldOrItemName);if(field)field.SetEnabled(aFieldOrItemName,enabled);}}
ControlPanel.prototype.CallOnModelChange=function(aField,aValue){if(this.ModelChangeActive)return;this.ModelChangeActive=true;if(this.OnModelChange){try{this.OnModelChange(aField,aValue);}catch(err){if(ControlPanels.Debug){alert('ControlPanel['+this.Name+'].CallModelChange:\nthis.OnModelChange call failed.\nError = '+err.message);}}}
this.ModelChangeActive=false;}
ControlPanel.prototype.AddHeader=function(aParams){aParams=xDefObj(aParams,{});var txt=xDefStr(aParams.Text,'&nbsp;');var colspan=xDefNum(aParams.ColSpan,1);var attr=xDefStr(aParams.Attr,'');if(colspan>1){if(attr)attr+=' ';attr+='colspan="'+colspan+'"';}
this.HeaderAttrs.push(attr);this.Headers.push(txt);return this;}
ControlPanel.prototype.AddField=function(aField){if(!xObj(aField))return this;this.Fields.push(aField);if(xFunc(aField.UpdateLayout))this.FieldUpdateLayoutFuncCounter++;return this;}
ControlPanel.prototype.GetFieldHtml=function(aField,aColIx){function StartTag(aClass,aColSpan,aAttr,aColIx){var css='';var colsp='';var attr='';if(aColSpan>1)colsp=' colspan="'+aColSpan+'"';if(aClass)css=aClass;css=' class="'+css+' Col'+aColIx+'"';if(aAttr)attr=' '+aAttr;var tag='<td'+colsp+css+attr+'>';return tag;};function EndTag(){return'</td>';};function LabelHtml(aLabel,aDescription){if(aLabel=='')return'';if(aDescription=='')return'<div class="FieldText">'+aLabel+'</div>';return'<div class="FieldText" title="'+aDescription+'">'+aLabel+'</div>';}
function AppendixHtml(aDescription,aLink,aHelpImage){if(this.HelpImage=='')return'';if(aLink){if(aDescription){return' '+'<a href="'+aLink+'" target="_blank" title="&rArr; '+aDescription+'"><img class="HelpImg" src="'+ControlPanel.prototype.ScriptPath+aHelpImage+'" alt=""></a>';}else{return' '+'<a href="'+aLink+'" target="_blank" title="&rArr; Infos"><img class="HelpImg" src="'+ControlPanel.prototype.ScriptPath+aHelpImage+'" alt=""></a>';}}else{if(aDescription){return' '+'<span><img class="HelpImg" src="'+ControlPanel.prototype.ScriptPath+aHelpImage+'" title="'+aDescription+'" alt=""></span>';}else{return'';}}}
var colspan=aField.ColSpan;var descr=aField.Description;var link=aField.Link;var attr=aField.Attr;var s='';if((colspan%2)==1){s+=StartTag(aField.LabelClass,1,'',aColIx)+LabelHtml(aField.Label,descr)+EndTag();s+=StartTag(aField.ValueClass,colspan,attr,aColIx+1)+aField.GetHtml()+AppendixHtml(descr,link,this.HelpImage)+EndTag();}else{s+=StartTag(aField.ValueClass,colspan,attr,aColIx)+aField.GetHtml()+AppendixHtml(descr,link,this.HelpImage)+EndTag();}
return s;}
ControlPanel.prototype.GetHtml=function(){var html='';var attr=this.Attr;var css=this.PanelClass+' NCols'+this.NCols;if(this.PanelFormat)css+=' '+this.PanelFormat;css=' class="'+css+'"';if(attr)attr=' '+attr;html+='<table id="'+this.Name+'"'+css+attr+'>';if(this.Headers.length>0){html+='<tr class="HdRow">';for(var i=0;i<this.Headers.length;i++){css=' class="HdCol'+(i+1)+'"';attr=this.HeaderAttrs[i];if(attr)attr=' '+attr;html+='<th'+css+attr+'>'+this.Headers[i]+'</th>';}
html+='</tr>';}
var thisCols=2*this.NCols;var coli=1;var rowi=1;var col=0;html+='<tr class="Row1">';for(var i=0;i<this.Fields.length;i++){var field=this.Fields[i];var s=this.GetFieldHtml(field,coli);html+=s;var nCols=1;var colspan=xDefNum(field.ColSpan,1);if((colspan%2)==1){nCols=1+colspan;coli++;}else{nCols=colspan;}
col+=nCols;coli++;if(((col%thisCols)==0)&&(i<(this.Fields.length-1))){rowi++;coli=1;html+='</tr><tr class="Row'+rowi+'">';}}
var remCols=thisCols-(col%thisCols);if(remCols==thisCols)remCols=0;var cs='';if(remCols>1){cs=' colspan="'+remCols+'"';html+='<td'+cs+' class="Col'+coli+'">&nbsp;</td>';}
html+='</tr></table>';return html;}
ControlPanel.prototype.Render=function(){document.writeln(this.GetHtml());return this;}
ControlPanel.prototype.Init=function(forceGetDefault){var me=this;this.DomObj=xGet(this.GetHtmlID());this.ForEachField(function CB_InitField(field){field.Init(forceGetDefault);});if(xDef(window.Tabs)&&this.FieldUpdateLayoutFuncCounter>0){if(this.DomObj){this.VisiChangeHandler=function(boxData){me.UpdateLayout(boxData.IsVisible?1:0);}
Tabs.AddVisiChangeHandler(this.DomObj,this.VisiChangeHandler);}}
if(!this.IsInit){this.LayoutChangeHandler=function(){me.UpdateLayout();}
xAddEventLayoutChange(this.LayoutChangeHandler);}
this.IsInit=true;this.Update();}
ControlPanel.prototype.SetDefaults=function(updateGui){var me=this;this.ForEachField(function CB_InitField(field){field.SetDefault();});if(updateGui)this.Update();}
ControlPanel.prototype.Invalidate=function(bUpdateGui){if(!this.IsInit)return;this.ForEachField(function CB_InvalidateField(field){field.Invalidate();});if(bUpdateGui)this.Update();}
ControlPanel.prototype.Refresh=function(bUpdateGui){if(!this.IsInit)return;this.ForEachField(function CB_RefreshField(field){field.Refresh();});if(bUpdateGui)this.Update();}
ControlPanel.prototype.Reset=function(bCallOnModelChange,bUpdateGui){if(!this.IsInit)return;var prevState=this.ModelChangeActive;this.ModelChangeActive=true;this.ForEachField(function CB_ResetField(field){field.Reset(false);});this.ModelChangeActive=prevState;if(bCallOnModelChange)this.CallOnModelChange(null);if(bUpdateGui)this.Update();}
ControlPanel.prototype.Update=function(){if(!this.IsInit)return;var oldFormatTab=NumFormatter.TableLike;NumFormatter.TableLike=this.FormatTab;this.ForEachField(function CB_UpdateField(field){field.Update();});NumFormatter.TableLike=oldFormatTab;if(this.LayoutHasChanged){this.UpdateLayout();this.LayoutHasChanged=false;}}
ControlPanel.prototype.UpdateLayout=function(visiState){if(!this.IsInit)return;visiState=xDefNum(visiState,-1);if(visiState==-1)visiState=xIsDisplayed(this.DomObj)?1:0;this.ForEachField(function CB_UpdateFieldLayout(field){field.UpdateLayout(visiState);});}
ControlPanel.prototype.IsDisplayed=function(){return xIsDisplayed(this.Name);}
ControlPanel.prototype.ParseWikiLink=function(aLink){if(aLink.indexOf('[[')!=0)return aLink;var pageName=aLink.substr(2,aLink.length-2);if(pageName.lastIndexOf(']]')!=pageName.length-2)return aLink;pageName=pageName.substring(0,pageName.length-2);var pars='';var subHeader='';var p=pageName.indexOf('~');if(p>0){pars=escape(pageName.substring(p+1));pars=pars.replace(/%3D/g,'=');pars=pars.replace(/%7E/g,'~');pars=pars.replace(/%20/g,'+');pars=pars.replace(/%5C%5C/g,'\\');pars=pars.replace(/%5C=/g,'%3D');pars=pars.replace(/%5C~/g,'%7E');pars=pars.replace(/\\/g,'%5C');pars=pars.replace(/~/g,'&');pars='&'+pars;pageName=pageName.substring(0,p);}
p=pageName.indexOf('#');if(p>=0){subHeader=escape(pageName.substring(p+1));subHeader=subHeader.replace(/%2E/g,'.');subHeader=subHeader.replace(/%2D/g,'-');subHeader=subHeader.replace(/%20/g,'_');subHeader=subHeader.replace(/%/g,'.');subHeader=subHeader.replace(/\+/g,'_');subHeader='#H_'+subHeader;pageName=pageName.substring(0,p);}
pageName=pageName.replace(/ /g,'+');return ASP_PAGE+'?page='+escape(pageName)+pars+subHeader;}
ControlPanel.prototype.AddEmptyField=function(aParams){var param={Label:'-',Html:'&nbsp;'};if(xObj(aParams)){if(xStr(aParams.Name))param.Name=aParams.Name;if(xNum(aParams.ColPan))param.ColSpan=aParams.ColSpan;if(xStr(aParams.Attr))param.Attr=aParams.Attr;}
return this.AddField(new CpHtmlField(this,param));}
ControlPanel.prototype.AddTextField=function(aParams){return this.AddField(new CpTextField(this,aParams));}
ControlPanel.prototype.AddHtmlField=function(aParams){return this.AddField(new CpHtmlField(this,aParams));}
function CpField(aParentPanel,aParams){aParams=xDefObj(aParams,{});this.Panel=aParentPanel;this.Name=aParentPanel.MakeFieldName(aParams.Name);this.DomObj=null;this.Default='';this.ValidDefault=false;this.OriginalValueRef=aParams.ValueRef;this.OriginalEnableRef=aParams.EnableRef;this.HtmlID=aParentPanel.MakeFieldHtmlID(this.Name);this.ValueRef=aParentPanel.MakeRef(aParams.ValueRef,this.Name);this.ReadOnly=xDefBool(aParams.ReadOnly,false);this.Enabled=xDefBool(aParams.Enabled,aParentPanel.Enabled);this.EnabledRef=aParentPanel.EnabledRef;if(xDef(aParams.EnabledRef)){if(xFunc(aParams.EnabledRef)){this.EnabledRef=aParams.EnabledRef;}else if(xStr(aParams.EnabledRef)){this.EnabledRef=aParentPanel.MakeRef(aParams.EnabledRef,'');}}
this.Label=xDefStr(aParams.Label,this.Name);if(this.Label=='-')this.Label='&nbsp;';this.Description=xDefStr(aParams.Description,'');this.Link=xDefStr(aParams.Link,'');if(this.Link)this.Link=aParentPanel.ParseWikiLink(this.Link);this.ColSpan=xDefNum(aParams.ColSpan,1);this.LabelClass=xDefStr(aParams.LabelClass,aParentPanel.LabelClass);this.ValueClass=xDefStr(aParams.ValueClass,aParentPanel.ValueClass);this.Attr=xDefStr(aParams.Attr,'');}
CpField.prototype.RefreshField=function(){this.HtmlID=this.Panel.MakeFieldHtmlID(this.Name);this.ValueRef=this.Panel.MakeRef(this.OriginalValueRef,this.Name);if(xDef(this.OriginalEnableRef)){if(xFunc(this.OriginalEnableRef)){this.EnabledRef=this.OriginalEnableRef;}else if(xStr(this.OriginalEnableRef)){this.EnabledRef=this.Panel.MakeRef(this.OriginalEnableRef,'');}}}
CpField.prototype.Free=function(){this.Panel=null;this.DomObj=null;}
CpField.prototype.GetValueRef=function(){return this.ValueRef.ValueRef;}
CpField.prototype.HasName=function(aName){return this.Name==aName;}
CpField.prototype.GetHtmlID=function(itemRef){return this.HtmlID;}
CpField.prototype.GetDomObj=function(itemRef){return this.DomObj?this.DomObj:xGet(this.HtmlID);}
CpField.prototype.ValueFromModel=function(aValueRef){try{var value=(aValueRef.RefStr)?aValueRef.ModelRef[aValueRef.PropRef]:0;}catch(e){if(ControlPanels.Debug){var modelRef=xDef(aValueRef.ModelRef)?'':'\nModelRef = undefined';alert('CpField.ValueFromModel: read of value failed.'+modelRef
+'\nPropRef = '+aValueRef.PropRef
+'\nRefStr = '+aValueRef.RefStr);}
value=0;}
if(xFunc(value)){return value(aValueRef);}
return value;}
CpField.prototype.ValueToModel=function(aValue,aValueRef,bUpdateGui,bCallOnModelChange,forceStore){bUpdateGui=xDefBool(bUpdateGui,false);forceStore=xDefBool(forceStore,false);bCallOnModelChange=xDefBool(bCallOnModelChange,false);var oldModelValue=this.ValueFromModel(aValueRef);var doChangeModel=forceStore||(oldModelValue!=aValue);if(doChangeModel){if(xFunc(aValueRef.ModelRef[aValueRef.PropRef])){aValueRef.ModelRef[aValueRef.PropRef](aValueRef,aValue);}else{aValueRef.ModelRef[aValueRef.PropRef]=aValue;}}
if(bCallOnModelChange&&this.Panel.OnModelChange===null)bUpdateGui=true;if(bUpdateGui)this.Update();if(doChangeModel&&bCallOnModelChange)this.Panel.CallOnModelChange(this,aValue);}
CpField.prototype.IsEnabled=function(){return this.Enabled;}
CpField.prototype.SetEnabled=function(ignoredItemName,enabled){if(this.EnabledRef==null)this.SetGuiEnabled(xDefBool(enabled,true));}
CpField.prototype.SetGuiEnabled=function(enabled){this.Enabled=enabled;}
CpField.prototype.UpdateLayout=function(visiState){}
function CpHtmlField(aPanel,aParams){aParams=xDefObj(aParams,{});this.parentClass.constructor.call(this,aPanel,aParams);this.Html=xDefStr(aParams.Html,'');this.LastHtml='';this.ValidLast=false;}
CpHtmlField.inheritsFrom(CpField);CpHtmlField.prototype.GetHtml=function(){return'<div id="'+this.HtmlID+'" class="HtmlField">'+xDefStr(this.Html,'&nbsp;')+'</div>';}
CpHtmlField.prototype.GetType=function(){return'HtmlField';}
CpHtmlField.prototype.Init=function(forceGetDefault){this.DomObj=xGet(this.HtmlID);}
CpHtmlField.prototype.SetDefault=function(){}
CpHtmlField.prototype.Invalidate=function(){this.ValidLast=false;}
CpHtmlField.prototype.Refresh=function(){this.RefreshField();this.ValidLast=false;}
CpHtmlField.prototype.Update=function(){if(!this.DomObj)return;var txt=this.Html;if(txt===''&&this.ValueRef.RefStr!=='')txt=this.ValueFromModel(this.ValueRef);if(txt==='')txt='&nbsp;';if(this.ValidLast&&this.LastHtml===txt)return;xInnerHTML(this.DomObj,txt);this.LastHtml=txt;this.ValidLast=true;}
CpHtmlField.prototype.Reset=function(){}
function CpTextField(aPanel,aParams){aParams=xDefObj(aParams,{});this.parentClass.constructor.call(this,aPanel,aParams);this.ConvToModelFunc=xDefFunc(aParams.ConvToModelFunc,null);this.ConvFromModelFunc=xDefFunc(aParams.ConvFromModelFunc,null);this.ReadOnly=xDefBool(aParams.ReadOnly,aPanel.ReadOnly);this.Mult=1;var m=aParams.Mult;if(xNum(m)||(xArray(m)&&m.length>=2&&xFunc(m[0])&&xFunc(m[1])))this.Mult=m;this.LowerLimit=xNum(aParams.LowerLimit)?aParams.LowerLimit:null;this.UpperLimit=xNum(aParams.UpperLimit)?aParams.UpperLimit:null;this.UnitsData=aPanel.MakeRef(aParams.UnitsData,'');this.MultRef=aPanel.MakeRef(aParams.MultRef,'');this.Units=xDefStr(aParams.Units,'');this.UnitsRef=aPanel.MakeRef(aParams.UnitsRef,'');this.Digits=xDefNum(aParams.Digits,aPanel.Digits);this.DigitsRef=aPanel.MakeRef(aParams.DigitsRef,'');this.Format=xDefStr(aParams.Format,aPanel.Format);this.FormatRef=aPanel.MakeRef(aParams.FormatRef,'');this.InputFormat=xDefStr(aParams.InputFormat,'');if(!this.ConvToModelFunc&&this.InputFormat!=''){if(this.InputFormat=='hms'){this.ConvToModelFunc=function(s){return NumFormatter.HmsStrToNum(s);};}else if(this.InputFormat=='dms'){this.ConvToModelFunc=function(s){return NumFormatter.DmsStrToNum(s);};}}
this.Inc=1;if(!this.FormatRef&&!this.DigitsRef&&this.Format.indexOf('fix')==0&&this.Digits>1){if(this.Digits==2){this.Inc=0.1;}else{this.Inc=0.01;}}
this.Inc=xDefNum(aParams.Inc,this.Inc);var me=this;this.OnChangeFunc=function(e){me.HandleChange(e);};this.OnKeyDownFunc=function(e){me.HandleKeyDown(e);};this.OnFocusFunc=function(e){me.HandleFocus(e);};this.LastValue=0;this.LastMult=1;this.LastUnits='';this.LastFormat='';this.LastDigits=0;this.ValidLast=false;}
CpTextField.inheritsFrom(CpField);CpTextField.prototype.Free=function(){if(!this.ReadOnly)this.RemoveEventHandlers();this.parentClass.Free.call(this);}
CpTextField.prototype.GetType=function(){return'TextField';}
CpTextField.prototype.GetHtml=function(){function InputTag(aName,aClass,aValue,bReadOnly){var css=''
if(bReadOnly)css='ReadOnly';if(aClass){if(css)css+=' ';css+=aClass;}
if(css)css=' class="'+css+'"';var readonly='';if(bReadOnly)readonly=' readonly="readonly"';var name='';if(aName)name=' name="'+aName+'" id="'+aName+'"';return'<input type="text"'+name+css+readonly+' value="'+aValue+'">';};function UnitHtml(aName,aUnits,aUnitsRef,aUnitsData,formatIsUnit){if(aUnits||aUnitsRef||(aUnitsData&&xDef(aUnitsData.Units))||formatIsUnit){return'<div class="FieldText" id="'+aName+'-Unit">'+aUnits+'</div>';}else{return'';}}
var input='';if(this.ValueRef)input=InputTag(this.HtmlID,this.Panel.FieldClass,'',this.ReadOnly);var unitsData=this.UnitsData;if(xObj(unitsData))unitsData=this.ValueFromModel(unitsData)
input+=UnitHtml(this.HtmlID,this.Units,this.UnitsRef,unitsData,this.Format==='unit');return input;}
CpTextField.prototype.HasUnits=function(){var unitsData=null;if(xObj(this.UnitsData))unitsData=this.ValueFromModel(this.UnitsData)
return(this.Units||this.UnitsRef||(unitsData&&xDef(unitsData.Units))||this.Format==='unit');}
CpTextField.prototype.AddEventHandlers=function(){xAddEvent(this.DomObj,'change',this.OnChangeFunc);xAddEvent(this.DomObj,'keydown',this.OnKeyDownFunc);xAddEvent(this.DomObj,'focus',this.OnFocusFunc);xAddEvent(this.DomObj,'click',this.OnFocusFunc);}
CpTextField.prototype.RemoveEventHandlers=function(){xRemoveEvent(this.DomObj,'change',this.OnChangeFunc);xRemoveEvent(this.DomObj,'keydown',this.OnKeyDownFunc);xRemoveEvent(this.DomObj,'focus',this.OnFocusFunc);xRemoveEvent(this.DomObj,'click',this.OnFocusFunc);}
CpTextField.prototype.GetEnabledFromModel=function(){if(this.EnabledRef==null)return this.Enabled;if(xFunc(this.EnabledRef))return this.EnabledRef();return this.ValueFromModel(this.EnabledRef);}
CpTextField.prototype.SetGuiEnabled=function(enabled,force){force=xDefBool(force,false);if(enabled){if(this.Enabled&&!force)return;if(this.DomObj&&!this.ReadOnly)this.DomObj.disabled=false;this.Enabled=true;}else{if(!this.Enabled&&!force)return;if(this.DomObj&&!this.ReadOnly)this.DomObj.disabled=true;this.Enabled=false;}}
CpTextField.prototype.SetFieldNum=function(aNum){if(this.Format){if(NumFormatter.IsXmsFormat(this.Format)){var numStr=NumFormatter.FormatXms(aNum,this.Format,this.Digits);this.DomObj.value=numStr;if(this.HasUnits()){xInnerHTML(this.HtmlID+'-Unit',this.Units);}}else{var format={Mode:this.Format,Precision:this.Digits};var numParts=NumFormatter.SplitNum(aNum,format);var numStr=NumFormatter.NumPartsToString(numParts);this.DomObj.value=numStr;if(this.HasUnits()){var prefix=NumFormatter.GetPrefixFromNumParts(numParts);xInnerHTML(this.HtmlID+'-Unit',prefix+this.Units);}}}else{this.DomObj.value=aNum.toString();if(this.HasUnits()){xInnerHTML(this.HtmlID+'-Unit',this.Units);}}}
CpTextField.prototype.Init=function(forceGetDefault){forceGetDefault=xDefBool(forceGetDefault,false);this.DomObj=xGet(this.HtmlID);if(!this.DomObj||!this.ValueRef)return;if(!this.ReadOnly)this.AddEventHandlers();if(!this.ValidDefault||forceGetDefault){this.Default=this.ValueFromModel(this.ValueRef);this.ValidDefault=true;}
this.ValidLast=false;var enable=this.GetEnabledFromModel();this.SetGuiEnabled(enable,true);}
CpTextField.prototype.SetDefault=function(){this.Default=this.ValueFromModel(this.ValueRef);this.ValidDefault=true;this.ValidLast=false;}
CpTextField.prototype.Invalidate=function(){this.ValidLast=false;}
CpTextField.prototype.Refresh=function(){this.RefreshField();this.Default=this.ValueFromModel(this.ValueRef);this.ValidDefault=true;this.ValidLast=false;}
CpTextField.prototype.Reset=function(bCallOnModelChange){if(this.ReadOnly)return;this.ValidLast=false;this.ValueToModel(this.Default,this.ValueRef,true,bCallOnModelChange);}
CpTextField.prototype.Store=function(inc,forceStore){forceStore=xDefBool(forceStore,false);if(!this.DomObj)return;var v;if(!this.ConvToModelFunc){if(xNum(this.Mult)){if(this.Mult!=0){v=NumFormatter.StringToNum(this.DomObj.value);if(isNaN(v)){this.Reset(true);return;}
v+=inc*this.Inc;v*=this.Mult;if(xNum(this.LowerLimit)&&v<this.LowerLimit)v=this.LowerLimit;if(xNum(this.UpperLimit)&&v>this.UpperLimit)v=this.UpperLimit;this.Invalidate();}else{v=this.DomObj.value;}}else{var ConvertInputToModelFunc=this.Mult[0];v=NumFormatter.StringToNum(this.DomObj.value);if(isNaN(v)){this.Reset(true);return;}
v=ConvertInputToModelFunc(v+inc*this.Inc);if(xNum(this.LowerLimit)&&v<this.LowerLimit)v=this.LowerLimit;if(xNum(this.UpperLimit)&&v>this.UpperLimit)v=this.UpperLimit;this.Invalidate();}}else{v=this.ConvToModelFunc(this.DomObj.value);if(xNum(v)){if(xNum(this.LowerLimit)&&v<this.LowerLimit)v=this.LowerLimit;if(xNum(this.UpperLimit)&&v>this.UpperLimit)v=this.UpperLimit;}
this.Invalidate();}
this.ValueToModel(v,this.ValueRef,true,true,forceStore);}
CpTextField.prototype.GetRefsFromModel=function(){var unitsData=null;function getSelection(modelRef,sel){var objRef=window;if(sel.indexOf('.')==0){sel=modelRef+sel;}
var refx=sel.replace(/\[([^\]]+)\]/g,'.$1');var refParts=refx.split('.');var last=refParts.length-1;for(var i=0;i<last;i++){objRef=objRef[refParts[i]];}
return objRef[refParts[last]];}
if(xObj(this.UnitsData))unitsData=this.ValueFromModel(this.UnitsData)
if(unitsData&&xDef(unitsData.Selection)){var selection=unitsData.Selection;if(xStr(selection))selection=getSelection(this.Panel.ModelRef,selection);if(xDef(unitsData.Mults))this.Mult=unitsData.Mults[selection];if(xDef(unitsData.Formats))this.Format=unitsData.Formats[selection];if(xDef(unitsData.Digits))this.Digits=unitsData.Digits[selection];if(xDef(unitsData.Units))this.Units=unitsData.Units[selection];}
if(this.MultRef)this.Mult=this.ValueFromModel(this.MultRef);if(this.FormatRef)this.Format=this.ValueFromModel(this.FormatRef);if(this.DigitsRef)this.Digits=this.ValueFromModel(this.DigitsRef);if(this.UnitsRef)this.Units=this.ValueFromModel(this.UnitsRef);}
CpTextField.prototype.CheckGuiUpdate=function(aValue){this.GetRefsFromModel();if(this.ValidLast){var updateNeedet=(this.LastValue!=aValue||this.LastMult!=this.Mult||this.LastUnits!=this.Units||this.LastFormat!=this.Format||this.LastDigits!=this.Digits);if(!updateNeedet){return false;}}
if(this.LastUnits!=this.Units){this.Panel.SetLayoutHasChanged();}
this.LastValue=aValue;this.LastMult=this.Mult;this.LastUnits=this.Units;this.LastFormat=this.Format;this.LastDigits=this.Digits;this.ValidLast=true;return true;}
CpTextField.prototype.Update=function(){if(!this.DomObj)return;var v=this.ValueFromModel(this.ValueRef);if(!this.ConvFromModelFunc){if(this.CheckGuiUpdate(v)){if(xNum(this.Mult)){if(this.Mult!=0){this.SetFieldNum(v/this.Mult);}else{this.DomObj.value=v;}}else{var ConvertModelToInputFunc=this.Mult[1];this.SetFieldNum(ConvertModelToInputFunc(v));}}}else{this.DomObj.value=this.ConvFromModelFunc(v);}
var enable=this.GetEnabledFromModel();this.SetGuiEnabled(enable);}
CpTextField.prototype.HandleChange=function(aEvent){if(!this.Enabled){aEvent.PreventDefault();return;}
this.Store(0,false);}
CpTextField.prototype.HandleKeyDown=function(aEvent){if(!this.Enabled){aEvent.PreventDefault();return true;}
var key=String.fromCharCode(aEvent.keyCode)
if(aEvent.keyCode==13||aEvent.keyCode==9){if(!this.DomObj)return true;var v=this.DomObj.value;var m=this.Mult;var isNumericMult=(xNum(m)&&m!==0)||xArray(m);if(isNumericMult&&v.replace(/\s+/g,'')===''){this.Reset(true);}else if(v===' '){this.Reset(true);}else{if(aEvent.keyCode==13){this.Store(0,true);this.DomObj.select();aEvent.PreventDefault();return false;}else{this.Store(0,false);}}
this.DomObj.select();}else if(!aEvent.shiftKey&&(aEvent.keyCode==38||aEvent.keyCode==40)){if(!this.DomObj)return true;var v=this.ValueFromModel(this.ValueRef);var m=this.Mult;var isNumericMult=(xNum(m)&&m!==0)||xArray(m);if(!isNumericMult||!xNum(v))return true;var inc=0;if(aEvent.keyCode==38){inc=1;}else if(aEvent.keyCode==40){inc=-1;}
if(aEvent.ctrlKey)inc*=10;if(aEvent.altKey)inc/=10;if(aEvent.ctrlKey&&aEvent.altKey)inc/=100;if(xArray(m)){v+=inc*this.Inc;}else{v+=inc*this.Inc*m;}
if(xNum(this.LowerLimit)&&v<this.LowerLimit)v=this.LowerLimit;if(xNum(this.UpperLimit)&&v>this.UpperLimit)v=this.UpperLimit;this.ValueToModel(v,this.ValueRef,true,true,true);this.Invalidate();aEvent.PreventDefault();this.DomObj.select();return false;}else if(aEvent.keyCode==27){this.Reset(true);this.DomObj.select();}else if((key=='Y')&&aEvent.ctrlKey&&!aEvent.altKey&&!aEvent.shiftKey){if(!this.DomObj)return true;this.DomObj.value='';return false;}
return true;}
CpTextField.prototype.HandleFocus=function(aEvent){if(!this.Enabled){aEvent.PreventDefault();return;}
this.DomObj.select();}
ControlPanel.prototype.CheckboxOrRadiobuttonHtml=function(aType,aName,aClass,bReadOnly,aItems,aNCols){var s='';var n=aItems.length-1;var nCols=aNCols;if(nCols<=0)nCols=n+1;if(nCols<=0)nCols=1;var nRows=Math.floor(n/nCols)+1;var colw=Math.floor(100/nCols);s+='<table class="FieldGrid">';var isCheckbox=(aType=='checkbox');var i=0;for(var row=1;row<=nRows;row++){s+='<tr>';for(var col=1;col<=nCols;col++){var css='FieldCell';var attrs='';if(row<=1)attrs+=' style="width:'+colw+'%;"';if((i<=n)&&(aItems[i].Text!='-')){if(isCheckbox){var name=this.MakeFieldHtmlID(aItems[i].Name);var id=name;bReadOnly=aItems[i].ReadOnly;}else{var name=this.MakeFieldHtmlID(aName);var id=name;if(n>0)id+='-'+i;}
if(bReadOnly)css+=' ReadOnly';attrs+=' class="'+css+'" id="'+id+'-Field"';s+='<td'+attrs+'>';attrs=' type="'+aType+'" id="'+id+'" name="'+name+'" class="'+aClass+'" value="'+aItems[i].Value+'"';if(bReadOnly)attrs+=' disabled="disabled"';s+='<div class="FieldText">'+'<input'+attrs+'>';s+='<span class="FieldCaption">'+aItems[i].Text+'&nbsp;</span></div>';}else{s+='<td>&nbsp;';}
s+='</td>';i++;}
s+='</tr>';}
s+='</table>';return s;}
ControlPanel.prototype.GetFieldCell=function(aInputElement){var domEle=aInputElement;while(domEle){domEle=xParent(domEle);if(xHasClass(domEle,'FieldCell'))return domEle;}
return null;}
ControlPanel.prototype.AddCheckboxField=function(aParams){return this.AddField(new CpCheckboxField(this,aParams));}
function CpCheckboxField(aPanel,aParams){aParams=xDefObj(aParams,{});this.parentClass.constructor.call(this,aPanel,aParams);this.Items=[];if(xArray(aParams.Items)){var itemsDef=aParams.Items;var nItemsDef=itemsDef.length;for(var i=0;i<nItemsDef;i++){var itemDef=itemsDef[i];if(itemDef){var item={};item.Name=xDefStr(itemDef.Name,this.Name+'-'+i);item.HtmlID=aPanel.MakeFieldHtmlID(item.Name);item.OriginalValueRef=itemDef.ValueRef;item.ValueRef=aPanel.MakeRef(itemDef.ValueRef,item.Name);item.Value=xDefStr(itemDef.Value,item.Name);item.Text=xDefStr(itemDef.Text,item.Name);if(item.Text=='-')item.Text='';item.ReadOnly=xDefBool(itemDef.ReadOnly,this.ReadOnly);item.Enabled=xDefBool(itemDef.Enabled,this.Enabled);item.OriginalEnabledRef=itemDef.EnabledRef;item.EnabledRef=this.EnabledRef;if(xDef(itemDef.EnabledRef)){if(xFunc(itemDef.EnabledRef)){item.EnabledRef=itemDef.EnabledRef;}else if(xStr(itemDef.EnabledRef)){item.EnabledRef=aPanel.MakeRef(itemDef.EnabledRef,'');}}
item.Default=false;item.ValidDefault=false;item.DomObj=null;item.LastValue='';item.ValidLast=false;item.CheckboxChangeHandler=null;item.CellClickHandler=null;this.Items.push(item);}}}
this.NCols=xDefNum(aParams.NCols,this.Items.length);}
CpCheckboxField.inheritsFrom(CpField);CpCheckboxField.prototype.GetType=function(){return'CheckboxField';}
CpCheckboxField.prototype.Free=function(){xArrForEach(this.Items,function CB_RemoveHandlers_ReleaseDomObj(item){var cbDomObj=item.DomObj;if(!item.ReadOnly&&cbDomObj&&item.CheckboxChangeHandler){xRemoveEvent(cbDomObj,'change',item.CheckboxChangeHandler);xRemoveEvent(cbDomObj,'click',item.CheckboxChangeHandler);var clickArea=this.Panel.GetFieldCell(cbDomObj);if(clickArea){xRemoveEvent(clickArea,'click',item.CellClickHandler);}}
item.DomObj=null;},this);this.parentClass.Free.call(this);}
CpCheckboxField.prototype.ForEachItem=function(func){xArrForEach(this.Items,func,this);}
CpCheckboxField.prototype.GetItem=function(aName){var item=xArrFind(this.Items,function CB_Compare_Name(item){return item.Name==aName;});return item?item:null;}
CpCheckboxField.prototype.HasName=function(aName){return(this.Name==aName||this.GetItem(aName)!=null);}
CpCheckboxField.prototype.GetValueRef=function(aItemName){var valueRef=this.ValueRef;if(xStr(aItemName)){var item=this.GetItem(aItemName);if(!tem)valueRef=item.ValueRef;}
return valueRef?valueRef.ValueRef:'';}
CpCheckboxField.prototype.GetHtmlID=function(itemName){if(!xStr(itemName))return this.HtmlID;var item=this.GetItem(itemName);return item?item.HtmlID:'';}
CpCheckboxField.prototype.GetDomObj=function(itemName){if(!xStr(itemName))return null;var item=this.GetItem(itemName);return item?item.DomObj:null;}
CpCheckboxField.prototype.GetEnabledFromModel=function(item){if(xStr(item)){item=this.GetItem(item);if(!item)return null;}else if(xNum(item)){item=this.Items[item];}
if(xFunc(item.EnabledRef)){return item.EnabledRef();}else if(item.EnabledRef){return this.ValueFromModel(item.EnabledRef);}
return item.Enabled;}
CpCheckboxField.prototype.IsEnabled=function(itemName){if(!xDef(itemName)||itemName==null)itemName='';var item=(itemName=='')?this.Items[0]:this.GetItem(itemName);return item?item.Enabled:true;}
CpCheckboxField.prototype.SetEnabled=function(itemRef,enabled){enabled=xDefBool(enabled,true);if(!xDef(itemRef)||itemRef==null)itemRef='';if(xStr(itemRef)&&itemRef==''){xArrForEach(this.Items,function CB_SetEnabled(item){if(item.EnabledRef==null)this.SetItemGuiEnabled(item,enabled);},this);}else{var item=xStr(itemName)?this.GetItem(itemName):itemName;if(item&&item.EnabledRef==null)this.SetItemGuiEnabled(item,enabled);}}
CpCheckboxField.prototype.SetItemGuiEnabled=function(item,enabled,force){var force=xDefBool(force,false);var fieldCellId=item.HtmlID+'-Field';if(enabled){if(item.Enabled&&!force)return;xRemoveClass(fieldCellId,'Disabled');item.Enabled=true;}else{if(!item.Enabled&&!force)return;xAddClass(fieldCellId,'Disabled');item.Enabled=false;}}
CpCheckboxField.prototype.OnChange=function(aEvent,aCheckboxIx){aEvent.StopPropagation();var item=this.Items[aCheckboxIx];if(!item.Enabled){aEvent.PreventDefault();return;}
if(!item.DomObj)return;var v=item.DomObj.checked;this.ValueToModel(v,item.ValueRef,false,true,false);}
CpCheckboxField.prototype.OnTextClick=function(aEvent,aCheckboxIx){var item=this.Items[aCheckboxIx];if(!item.Enabled)return;var domObj=item.DomObj;if(domObj)domObj.checked=!domObj.checked;this.OnChange(aEvent,aCheckboxIx);}
CpCheckboxField.prototype.GetHtml=function(){return this.Panel.CheckboxOrRadiobuttonHtml('checkbox','','CheckBox',this.ReadOnly,this.Items,this.NCols);}
CpCheckboxField.prototype.Init=function(forceGetDefault){function GetChangeCallback(self,i){return function CB_OnChange(e){self.OnChange(e,i);};}
function GetTextClickCallback(self,i){return function CB_OnTextClick(e){self.OnTextClick(e,i);};}
forceGetDefault=xDefBool(forceGetDefault,false);var nItems=this.Items.length;for(var i=0;i<nItems;i++){var item=this.Items[i];var cbDomObj=xGet(item.HtmlID);if(!item.ReadOnly){item.CheckboxChangeHandler=GetChangeCallback(this,i);xAddEvent(cbDomObj,'change',item.CheckboxChangeHandler);xAddEvent(cbDomObj,'click',item.CheckboxChangeHandler);var clickArea=this.Panel.GetFieldCell(cbDomObj);if(clickArea){item.CellClickHandler=GetTextClickCallback(this,i);xAddEvent(clickArea,'click',item.CellClickHandler);}}
item.DomObj=cbDomObj;if(!item.ValidDefault||forceGetDefault){item.Default=this.ValueFromModel(item.ValueRef);item.ValidDefault=true;}
item.ValidLast=false;var enable=this.GetEnabledFromModel(item);this.SetItemGuiEnabled(item,enable,true);}}
CpCheckboxField.prototype.SetDefault=function(){var nItems=this.Items.length;for(var i=0;i<nItems;i++){var item=this.Items[i];item.Default=this.ValueFromModel(item.ValueRef);item.ValidDefault=true;item.ValidLast=false;}}
CpCheckboxField.prototype.Invalidate=function(){this.ForEachItem(function CB_InvalidateItem(item,i){item.ValidLast=false;});}
CpCheckboxField.prototype.Refresh=function(){this.RefreshField();var nItemsDef=this.Items.length;for(var i=0;i<nItemsDef;i++){var item=this.Items[i];item.HtmlID=this.Panel.MakeFieldHtmlID(item.Name);item.ValueRef=this.Panel.MakeRef(item.OriginalValueRef,item.Name);if(xDef(item.OriginalEnableRef)){if(xFunc(item.OriginalEnableRef)){item.EnabledRef=item.OriginalEnableRef;}else if(xStr(item.OriginalEnableRef)){item.EnabledRef=this.Panel.MakeRef(item.OriginalEnableRef,'');}}}
this.ForEachItem(function CB_RefreshItem(item,i){item.ValidLast=false;});}
CpCheckboxField.prototype.Reset=function(bCallModelChangeCB){this.ForEachItem(function CB_ResetItem(item,i){if(item.ReadOnly)return;item.ValidLast=false;this.ValueToModel(item.Default,item.ValueRef,false,bCallModelChangeCB);});}
CpCheckboxField.prototype.Update=function(){var nItems=this.Items.length;for(var i=0;i<nItems;i++){var item=this.Items[i];var v=this.ValueFromModel(item.ValueRef);if(!(item.ValidLast&&v==item.LastValue)){if(item.DomObj)item.DomObj.checked=v;item.LastValue=v;item.ValidLast=true;}
var enable=this.GetEnabledFromModel(item);this.SetItemGuiEnabled(item,enable);}}
ControlPanel.prototype.AddRadiobuttonField=function(aParams){return this.AddField(new CpRadiobuttonField(this,aParams));}
function CpRadiobuttonField(aPanel,aParams){aParams=xDefObj(aParams,{});this.parentClass.constructor.call(this,aPanel,aParams);this.ValueType=xDefStr(aParams.ValueType,'str');this.Items=[];if(xArray(aParams.Items)){var itemsDef=aParams.Items;var nItemsDef=itemsDef.length;for(var i=0;i<nItemsDef;i++){var itemDef=itemsDef[i];if(itemDef){var item={};item.Name=xDefStr(itemDef.Name,this.Name+'-'+i);item.HtmlID=this.HtmlID+'-'+i;if(xDef(itemDef.Value)){item.Value=this.ToValueStr(itemDef.Value,this.ValueType);}else{item.Value=item.Name;}
item.Text=xDefStr(itemDef.Text,item.Name);item.DomObj=null;item.RadiobuttonChangeHandler=null;this.Items.push(item);}}}
this.NCols=xDefNum(aParams.NCols,this.Items.length);this.LastValue='';this.ValidLast=false;}
CpRadiobuttonField.inheritsFrom(CpField);CpRadiobuttonField.prototype.GetType=function(){return'RadiobuttonField';}
CpRadiobuttonField.prototype.Free=function(){xArrForEach(this.Items,function CB_RemoveEventHandlers_ReleaseDomObj(item,i){var rbDomObj=item.DomObj;if(!this.ReadOnly&&rbDomObj&&item.RadiobuttonChangeHandler){xRemoveEvent(rbDomObj,'change',item.RadiobuttonChangeHandler);xRemoveEvent(rbDomObj,'click',item.RadiobuttonChangeHandler);var clickArea=this.Panel.GetFieldCell(rbDomObj);if(clickArea){xRemoveEvent(clickArea,'click',item.RadiobuttonChangeHandler);}}
item.DomObj=null;},this);this.parentClass.Free.call(this);}
CpRadiobuttonField.prototype.ForEachItem=function(func){xArrForEach(this.Items,func,this);}
CpRadiobuttonField.prototype.GetItem=function(aName){var item=xArrFind(this.Items,function CB_Compare_Name(item){return item.Name==aName;});return item?item:null;}
CpRadiobuttonField.prototype.ParseDataType=function(aValue,aDataType){var v=aValue;if(aDataType=='int'){v=parseInt(v,10);if(isNaN(v))v=0;}else if(aDataType=='num'){v=parseFloat(v);if(isNaN(v))v=0.0;}else if(aDataType=='bool'){v=(aValue!=''&&aValue!='0'&&aValue!='false');}
return v;}
CpRadiobuttonField.prototype.ToValueStr=function(aValue,aDataType){var v=aValue;if(xStr(v)){if(aDataType=='int'){v=this.ParseDataType(aValue,aDataType).toString();}else if(aDataType=='num'){v=parseFloat(aValue);v=(IsNaN(v))?'0.0':aValue;}else if(aDataType=='bool'){v=(aValue!=''&&aValue!='0'&&aValue!='false');v=(v)?'true':'false';}}else{if(aDataType=='int'){v=xNum(aValue)?v.toFixed(0):'0';}else if(aDataType=='num'){v=xNum(aValue)?v.toString():'0.0';}else if(aDataType=='bool'){v=aValue?'true':'false';}else{v=aValue.toString();}}
return v;}
CpRadiobuttonField.prototype.GetEnabledFromModel=function(){if(xFunc(this.EnabledRef)){return this.EnabledRef();}else if(this.EnabledRef!=null){return this.ValueFromModel(this.EnabledRef);}
return this.Enabled;}
CpRadiobuttonField.prototype.SetGuiEnabled=function(enabled,force){var force=xDefBool(force,false);var fieldCellId=this.HtmlID;if(enabled){if(this.Enabled&&!force)return;this.ForEachItem(function CB_SetItemEnabled(item,i){xRemoveClass(fieldCellId+'-'+i+'-Field','Disabled');});this.Enabled=true;}else{if(!this.Enabled&&!force)return;this.ForEachItem(function CB_SetItemDisabled(item,i){xAddClass(fieldCellId+'-'+i+'-Field','Disabled');});this.Enabled=false;}}
CpRadiobuttonField.prototype.OnChange=function(aEvent,aRadioIx){aEvent.StopPropagation();if(!this.Enabled){aEvent.PreventDefault();this.ValidLast=false;this.Update();return;}
var item=this.Items[aRadioIx];var v=this.ParseDataType(item.Value,this.ValueType);this.ValueToModel(v,this.ValueRef,false,true,false);}
CpRadiobuttonField.prototype.GetHtml=function(){return this.Panel.CheckboxOrRadiobuttonHtml('radio',this.Name,'Radio',this.ReadOnly,this.Items,this.NCols);}
CpRadiobuttonField.prototype.GetHtmlID=function(itemIx){if(!xNum(itemIx))return this.HtmlID;if(itemIx<0||itemIx>=this.Items.length)return'';return this.Items[itemIx].HtmlID;}
CpRadiobuttonField.prototype.GetDomObj=function(itemIx){if(!xNum(itemIx))return null;if(itemIx<0||itemIx>=this.Items.length)return null;return this.Items[itemIx].DomObj;}
CpRadiobuttonField.prototype.Init=function(forceGetDefault){function GetChangeHandler(self,i){return function CB_OnChange(e){self.OnChange(e,i);};}
forceGetDefault=xDefBool(forceGetDefault,false);if(!this.ValidDefault||forceGetDefault){this.Default=this.ValueFromModel(this.ValueRef);this.ValidDefault=true;}
var nItems=this.Items.length;for(var i=0;i<nItems;i++){var item=this.Items[i];var rbDomObj=xGet(item.HtmlID);if(!this.ReadOnly){item.RadiobuttonChangeHandler=GetChangeHandler(this,i);xAddEvent(rbDomObj,'change',item.RadiobuttonChangeHandler);xAddEvent(rbDomObj,'click',item.RadiobuttonChangeHandler);var clickArea=this.Panel.GetFieldCell(rbDomObj);if(clickArea)xAddEvent(clickArea,'click',item.RadiobuttonChangeHandler);}
item.DomObj=rbDomObj;}
var enable=this.GetEnabledFromModel();this.SetGuiEnabled(enable,true);this.ValidLast=false;}
CpRadiobuttonField.prototype.SetDefault=function(){this.Default=this.ValueFromModel(this.ValueRef);this.ValidDefault=true;this.ValidLast=false;}
CpRadiobuttonField.prototype.Invalidate=function(){this.ValidLast=false;}
CpRadiobuttonField.prototype.Refresh=function(){this.RefreshField();this.Default=this.ValueFromModel(this.ValueRef);this.ValidDefault=true;this.ValidLast=false;}
CpRadiobuttonField.prototype.Reset=function(bCallModelChangeCB){if(this.ReadOnly)return;this.ValidLast=false;this.ValueToModel(this.Default,this.ValueRef,false,bCallModelChangeCB);}
CpRadiobuttonField.prototype.Update=function(){var v=this.ValueFromModel(this.ValueRef);if(!(this.ValidLast&&v==this.LastValue)){this.LastValue=v;this.ValidLast=true;var nItems=this.Items.length;for(var i=0;i<nItems;i++){var ov=this.ParseDataType(this.Items[i].Value,this.ValueType);if(v==ov){var domObj=this.Items[i].DomObj;if(domObj)domObj.checked=true;break;}}}
var enable=this.GetEnabledFromModel();this.SetGuiEnabled(enable);}
ControlPanel.prototype.AddValueSliderField=function(aParams){if(!xDef(aParams))aParams={};var valuePos=xDefStr(this.ValuePos,'left');aParams.IsValueSlider=true;if(valuePos=='right'){this.AddSliderField(aParams);aParams.ColSpan=2;var textField=new CpTextField(this,aParams);textField.ValueClass='Value SliderValue';this.AddField(textField);}else{var textField=new CpTextField(this,aParams);textField.ValueClass='Value SliderValue';this.AddField(textField);aParams.ColSpan=2;this.AddSliderField(aParams);}
return this;}
ControlPanel.prototype.AddSliderField=function(aParams){return this.AddField(new CpSliderField(this,aParams));}
function CpSliderField(aPanel,aParams){aParams=xDefObj(aParams,{});this.parentClass.constructor.call(this,aPanel,aParams);this.HtmlID+='-Slider'
if(!xStr(aParams.ValueClass))this.ValueClass='Value Slider';if(xStr(aParams.SliderValueRef)){this.ValueRef=aPanel.MakeRef(aParams.SliderValueRef,this.Name);}
if(aParams.IsValueSlider){this.ReadOnly=xDefBool(aParams.SliderReadOnly,false);}
this.Caption=xDefStr(aParams.Caption,'&hArr;');this.Min=xDefNum(aParams.Min,0);this.Max=xDefNum(aParams.Max,1);this.Steps=xDefNum(aParams.Steps,0);this.Slide=xDefBool(aParams.Slide,false);this.Rounding=xDefStr(aParams.Rounding,'none');this.BorderWidth=xDefNum(aParams.BorderWidth,1);this.Color=xDefStr(aParams.Color,'');this.LogScale=xDefBool(aParams.LogScale,false);this.SnapTo=null;if(xArray(aParams.SnapTo)||xNum(aParams.SnapTo))this.SnapTo=aParams.SnapTo;this.SnapRange=xDefNum(aParams.SnapRange,100);this.DgdSlider=null;this.LastValue=0;this.LastSliderPos=-1;}
CpSliderField.inheritsFrom(CpField);CpSliderField.prototype.Free=function(){this.DgdSlider.free();this.DgdSlider=null;this.parentClass.Free.call(this);}
CpSliderField.prototype.GetType=function(){return'SliderField';}
CpSliderField.prototype.GetEnabledFromModel=function(){if(xFunc(this.EnabledRef)){return this.EnabledRef();}else if(this.EnabledRef!=null){return this.ValueFromModel(this.EnabledRef);}
return this.Enabled;}
CpSliderField.prototype.SetGuiEnabled=function(enabled,force){if(enabled){if(this.Enabled&&!force)return;if(this.DgdSlider&&!this.ReadOnly)this.DgdSlider.enable();this.Enabled=true;}else{if(!this.Enabled&&!force)return;if(this.DgdSlider&&!this.ReadOnly)this.DgdSlider.disable();this.Enabled=false;}}
CpSliderField.prototype.GetHtml=function(){return DgdSliderHtml(this.HtmlID,this.Caption,this.Color);}
CpSliderField.prototype.Init=function(forceGetDefault){forceGetDefault=xDefBool(forceGetDefault,false);var self=this;var options={};if(this.BorderWidth>0){options.right=2*this.BorderWidth;}
if(this.Steps>0){options.steps=this.Steps+1;options.snap=true;}
options.slide=this.Slide;options.animationCallback=function(x,y){self.OnSliderChange(x,y);}
this.DgdSlider=new DgdSlider(this.HtmlID,options);this.DomObj=xGet(this.HtmlID);if(this.ReadOnly){this.DgdSlider.readonly();xAddClass(this.DomObj,'ReadOnly');if(this.Caption=='&hArr;'){xInnerHTML(this.HtmlID+'-Handle','|');}}
if(!this.ValidDefault||forceGetDefault){this.Default=this.ValueFromModel(this.ValueRef);this.ValidDefault=true;}
var enable=this.GetEnabledFromModel();this.SetGuiEnabled(enable,true);}
CpSliderField.prototype.SetDefault=function(){this.Default=this.ValueFromModel(this.ValueRef);this.ValidDefault=true;}
CpSliderField.prototype.Invalidate=function(){this.LastSliderPos=-1;}
CpSliderField.prototype.Refresh=function(){this.RefreshField();this.Default=this.ValueFromModel(this.ValueRef);this.ValidDefault=true;this.LastSliderPos=-1;}
CpSliderField.prototype.Reset=function(bCallModelChangeCB){if(this.ReadOnly)return;this.ValueToModel(this.Default,this.ValueRef,false,bCallModelChangeCB);}
CpSliderField.prototype.OnSliderChange=function(x,y){var snapped=false;if(!this.DgdSlider||this.ReadOnly)return;var v=x*(this.Max-this.Min)+this.Min;if(xArray(this.SnapTo)){var smallestSnapDistance=(this.Max-this.Min);var smallestSnapIndex=-1;for(var i=0;i<this.SnapTo.length;i++){var snapDistance=Math.abs(v-this.SnapTo[i]);if(snapDistance<smallestSnapDistance){smallestSnapDistance=snapDistance;smallestSnapIndex=i;}}
var snapDelta=(this.Max-this.Min)/this.SnapRange;if(smallestSnapIndex>=0&&smallestSnapDistance<snapDelta){v=this.SnapTo[smallestSnapIndex];snapped=true;}}else if(xNum(this.SnapTo)){var snapDelta=(this.Max-this.Min)/this.SnapRange;if(Math.abs(v-this.SnapTo)<snapDelta){v=this.SnapTo;snapped=true;}}
if(this.Rounding=='floor'){v=Math.floor(v);}else if(this.Rounding=='round'){v=Math.round(v);}
if(v<this.Min)v=this.Min;if(v>this.Max)v=this.Max;if(this.LogScale){v=Math.pow(10,v);}
this.LastValue=v;this.LastSliderPos=x;this.ValueToModel(v,this.ValueRef,false,true,false);if(snapped){this.LastSliderPos=-1;this.Update();}}
CpSliderField.prototype.Update=function(){if(!this.DgdSlider)return;var v=this.ValueFromModel(this.ValueRef);if(this.LastSliderPos==-1||this.LastValue!=v){var vs=v;if(this.LogScale){vs=Math.log10(v);}
var x=(vs-this.Min)/(this.Max-this.Min);if(x<0)x=0;if(x>1)x=1;this.DgdSlider.setValue(x,0,true);this.LastValue=v;this.LastSliderPos=x;}
var enable=this.GetEnabledFromModel();this.SetGuiEnabled(enable);}
CpSliderField.prototype.UpdateLayout=function(visiState){if(!this.DgdSlider||visiState==0)return;this.DgdSlider.updateLayout();if(this.LastSliderPos==-1){this.Update();}else{this.DgdSlider.setValue(this.LastSliderPos,0,true);}}