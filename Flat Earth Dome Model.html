<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="Interactive Flat Earth Dome Model with realistic planetary motion, including retrograde behavior. Shows sunrise, sunset, moon phases, eclipses, star trails, and planetary movements.">
    <meta name="keywords" content="FlatEarth, Interactive, Planets, Retrograde Motion, Astronomy, Dome Model">
    <meta name="author" content="Enhanced Flat Earth Model">

    <title>Interactive Flat Earth Dome Model with Planetary Motion</title>

    <!-- Core Configuration -->
    <script>
        var APP_CONFIG = {
            NUMBER_FORMATTING: 'en',
            PAGE_NAME: 'Interactive Flat Earth Dome Model',
            ENABLE_PLANETS: true,
            ENABLE_RETROGRADE: true
        };
    </script>

    <!-- External Libraries -->
    <script src="./Flat Earth Dome Model_files/jsg.js.download"></script>
    <script src="./Flat Earth Dome Model_files/jsgx3d.js.download"></script>
    <script src="./Flat Earth Dome Model_files/jsgMouseHandler.js.download"></script>
    <script src="./Flat Earth Dome Model_files/EarthMap.js.download"></script>
    <script src="./Flat Earth Dome Model_files/NumFormatter.js.download"></script>
    <script src="./Flat Earth Dome Model_files/Slider.js.download"></script>
    <script src="./Flat Earth Dome Model_files/ControlPanel.js.download"></script>
    <script src="./Flat Earth Dome Model_files/Tabs.js.download"></script>
    <script src="./Flat Earth Dome Model_files/DataX.js.download"></script>
    <script src="./Flat Earth Dome Model_files/xtc.js.download"></script>
    <script src="./Flat Earth Dome Model_files/ModelAnimation.js.download"></script>

    <!-- Stylesheets -->
    <link rel="stylesheet" type="text/css" href="./Flat Earth Dome Model_files/styles.css" media="all">
    <link rel="stylesheet" type="text/css" href="./Flat Earth Dome Model_files/style1.css" media="all">
</head>
<body id="body">
</head>
<body id="body">
    <div class="page" id="Layout-Root">
        <div class="headerSection pageSection" id="Layout-HeaderSection">
            <div class="pageFrame" id="Layout-HeaderFrame">
                <div class="pageRow100">
                    <div class="brandingCol" id="Layout-BrandingPart">
                        <div class="branding" id="Layout-Branding">
                            <h1>Interactive Flat Earth Dome Model</h1>
                            <h2>with Planetary Motion & Retrograde Behavior</h2>
                        </div>
                    </div>
                </div>
            </div>
            <div class="headertext" id="Layout-HeaderTextAndImage">
                <h2>Enhanced Interactive Flat Earth Model with Planetary Motion</h2>
            </div>

</div>
<div class="contentSection pageSection" id="Layout-MainSection">
<div class="pageFrame" id="Layout-MainFrame">
<div class="pageRow100">
<div id="SidebarOnOffButtons">
<div id="SidebarOffButton" class="sidebarOnOffButton">
<a href="javascript:LayoutMaximize()">�</a></div>
<div id="SidebarOnButton" class="sidebarOnOffButton hide">
<a href="javascript:LayoutNormal()">�</a></div>
</div>
<div id="FullscreenOnOffButtons">
<div id="FullscreenOffButton" class="sidebarOnOffButton hide">
<a href="javascript:LayoutFullscreenOff()">[&#8201;&#8722;&#8201;]</a></div>
<div id="FullscreenOnButton" class="sidebarOnOffButton">
<a href="javascript:LayoutFullscreenOn()">[&#8201;+&#8201;]</a></div>
</div>
            <div class="layoutCol75" id="Layout-ContentPart">
                <!-- Enhanced Flat Earth Dome Model with Planetary Motion -->

                <!-- Tab Navigation -->
                <ul id="DomeDemoTabs" class="TabSelectors Top TopMargin">
                    <li class="TabButton TabEnabled " id="IntroButton">Intro</li>
                    <li class="TabButton TabEnabled " id="EclipsesButton">Eclipses</li>
                    <li class="TabButton TabEnabled " id="EquinoxButton">Equinox</li>
                    <li class="TabButton TabEnabled " id="DayNightButton">DayNight</li>
                    <li class="TabButton TabEnabled " id="PolesButton">Poles</li>
                    <li class="TabButton TabEnabled " id="StarsButton">Stars</li>
                    <li class="TabButton TabEnabled " id="PlanetsButton">Planets</li>
                    <li class="TabButton TabEnabled " id="ResetButton">Reset</li>
                    <li class="TabButton" id="BackButton">&lt;&lt;</li>
                    <li class="TabButton" id="PlayButton">Play</li>
                    <li class="TabButton" id="ForwButton">&gt;&gt;</li>
                    <li class="TabButton" id="CountButton">x</li>
                    <li class="TabButton" id="ResumeButton">Resume</li>
                </ul>

                <!-- Planetary Motion System -->
                <script src="Flat Earth Dome Model_files/planetary-motion.js"></script>

                <!-- Main Application Script -->
                <script src="Flat Earth Dome Model_files\script.js"></script>



</div></div>

<script>
var SEL_GRP_AutoName1 = new Array('AutoName1ON','AutoName1OFF');
var SEL_GRPN_AutoName1 = new Array('AutoName1ON','AutoName1OFF');
</script>
<div class="OnOff SaveRestorePanel"><div id="SEL_ELE_AutoName1OFF" class="selele">
<div class="noprint"><div class="OnOffSign">+</div><a href="javascript:" onclick="SEL(SEL_GRP_AutoName1,[&#39;AutoName1ON&#39;],true);return false;"><span><div class="OnOffHead">Show Save-Restore Panel</div></span></a></div>
</div>

<div id="SEL_ELE_AutoName1ON" style="display:none;" class="selele">
<div class="noprint"><div class="OnOffSign">&#8722;</div><a href="javascript:" onclick="SEL(SEL_GRP_AutoName1,[&#39;AutoName1OFF&#39;],true);return false;"><span><div class="OnOffHead">Hide Save-Restore Panel</div></span></a></div><div class="OnOffText noborder">

<textarea id="SaveRestorePanel" name="SaveRestorePanel" spellcheck="false" class="ListingDisplay" rows="8" cols="60"></textarea>


<p>
<a href="javascript:void%20DataX.GetAppState(true)" title="JavaScript"><span class="lbtn">Get App State</span></a> <a href="javascript:void%20DataX.GetAppStateUrl(ThisPageUrl)" title="JavaScript"><span class="lbtn">Get App Url</span></a> <a href="javascript:void%20DataX.SetAppState()" title="JavaScript"><span class="lbtn lbtnGreen">Set App State</span></a> <a href="javascript:void%20DataX.ClearSaveRestoreDomObj()" title="JavaScript"><span class="lbtn lbtnRed">Clear</span></a>
</p>

</div>
</div>

</div>

</div>
</div>
</div>
</div>
</div>



</body></html>