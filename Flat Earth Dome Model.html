<!DOCTYPE html>
<html lang="en" data-lt-installed="true"><head><meta http-equiv="Content-Type" content="text/html; charset=windows-1252">

<meta name="viewport" content="width=device-width, initial-scale=1.0">
<meta http-equiv="expires" content="0">
<meta http-equiv="imagetoolbar" content="no">
<meta name="robots" content="INDEX,FOLLOW">
<meta name="generator" content="paradome">
<meta name="publisher" content="Enhanced Flat Earth Model">
<meta name="description" content="Here is an interactive Flat Earth Model that can show Sunrise, Sunset, Moonrise, Moonset, Moon Phases, Moon s apparent rotation, Sun s position on Equinox, Seasons, some aspects of Solar and Lunar Eclipses, Star trails, 24 hours Day Night at the Northpole and Antarctica, Celestial Poles, why people south of the equator can see the same Stars rotate clockwise around a singe celestial pole at the same time from different continents. Enhanced with realistic planetary motion and retrograde behavior.">
<meta name="keywords" content="FlatEarth, Interactive, Planets, Retrograde Motion, Knowledge">

<title>Enhanced Flat Earth Dome Model with Planetary Motion</title>

<script>
var ROOT              = '../';
var NUMBER_FORMATING  = 'en';
var ASP_PAGE          = 'index.asp';
var ASP_REF           = 'index.asp?page=Enhanced+Flat+Earth+Dome+Model&state=--690-10-11-10-189.9-2118.26968-30-10-10-11-11-10-20-10-31-2';
var PAGE_NAME         = 'Enhanced Flat Earth Dome Model';
var PAGE_URL          = 'index.asp?page=Enhanced+Flat+Earth+Dome+Model';
var MEDIA_FOLDER      = '../blog/media/';
var WIKIDOC_URL       = '../wiki_doc/index.asp?page=PageName';
var PAGE_MODE         = '';
var MATH_PROCESSOR    = 'MathJax';
var ENABLE_EDIT       = true;
var HAS_FORMS         = false;
var IS_LOGED_IN       = false;
var IS_USER_LOGED_IN  = false;
var WIKI_HiliWordList   = [  ];
</script>

<script type="text/x-mathjax-config;executed=true">
MathJax.Hub.Config({
 displayAlign: 'left',
 displayIndent: '0',
 elements: ['Wiki'],
 preJax: '<MathJax>',
 postJax: '</MathJax>',
 tex2jax: {
  inlineMath: [],
  displayMath: [],
  balanceBraces: false
 },
});
</script>
<script async="" src="./Flat Earth Dome Model_files/MathJax.js.download"></script>
<script src="./Flat Earth Dome Model_files/wiki.js.download"></script>


<link rel="stylesheet" type="text/css" href="./Flat Earth Dome Model_files/styles.css" media="all">
<link rel="stylesheet" type="text/css" href="./Flat Earth Dome Model_files/style1.css" media="all">
</head>
<body id="body">

  <div style="visibility: hidden; overflow: hidden; position: absolute; top: 0px; height: 1px; width: auto; padding: 0px; border: 0px; margin: 0px; text-align: left; text-indent: 0px; text-transform: none; line-height: normal; letter-spacing: normal; word-spacing: normal;">
    <div id="MathJax_SVG_Hidden"></div>
    </div>

<div class="page notEditMode limitWidth" id="Layout-Root">
<div class="headerSection pageSection" id="Layout-HeaderSection">
<div class="pageFrame" id="Layout-HeaderFrame">
<div class="pageRow100">
<div class="brandingCol" id="Layout-BrandingPart">
<div class="branding" id="Layout-Branding">
<h1><span>Enhanced Flat Earth Dome Model</span></h1>
<h2>with Planetary Motion & Retrograde Behavior</h2>
</div>
</div>
</div>
</div>
<div class="headertext" id="Layout-HeaderTextAndImage">
<h2>Interactive Flat Earth Model with Realistic Planetary Motion</h2>
</div>

</div>
<div class="contentSection pageSection" id="Layout-MainSection">
<div class="pageFrame" id="Layout-MainFrame">
<div class="pageRow100">
<div id="SidebarOnOffButtons">
<div id="SidebarOffButton" class="sidebarOnOffButton">
<a href="javascript:LayoutMaximize()">�</a></div>
<div id="SidebarOnButton" class="sidebarOnOffButton hide">
<a href="javascript:LayoutNormal()">�</a></div>
</div>
<div id="FullscreenOnOffButtons">
<div id="FullscreenOffButton" class="sidebarOnOffButton hide">
<a href="javascript:LayoutFullscreenOff()">[&#8201;&#8722;&#8201;]</a></div>
<div id="FullscreenOnButton" class="sidebarOnOffButton">
<a href="javascript:LayoutFullscreenOn()">[&#8201;+&#8201;]</a></div>
</div>
            <div class="layoutCol75" id="Layout-ContentPart">


<script src="./Flat Earth Dome Model_files/jsg.js.download"></script>


<script src="./Flat Earth Dome Model_files/jsgx3d.js.download"></script>

<script src="./Flat Earth Dome Model_files/jsgMouseHandler.js.download"></script>

<script src="./Flat Earth Dome Model_files/EarthMap.js.download"></script>

<script src="./Flat Earth Dome Model_files/NumFormatter.js.download"></script>
<script src="./Flat Earth Dome Model_files/Slider.js.download"></script>
<script src="./Flat Earth Dome Model_files/ControlPanel.js.download"></script>

<script src="./Flat Earth Dome Model_files/Tabs.js.download"></script>

<script src="./Flat Earth Dome Model_files/DataX.js.download"></script>

<script src="./Flat Earth Dome Model_files/xtc.js.download"></script>

<script src="./Flat Earth Dome Model_files/ModelAnimation.js.download"></script>

<ul id="DomeDemoTabs" class="TabSelectors Top TopMargin">
<li class="TabButton TabEnabled " id="IntroButton">Intro</li>
<li class="TabButton TabEnabled " id="EclipsesButton">Eclipses</li>
<li class="TabButton TabEnabled " id="EquinoxButton">Equinox</li>
<li class="TabButton TabEnabled " id="DayNightButton">DayNight</li>
<li class="TabButton TabEnabled " id="PolesButton">Poles</li>
<li class="TabButton TabEnabled " id="StarsButton">Stars</li>
<li class="TabButton TabEnabled " id="PlanetsButton">Planets</li>
<li class="TabButton TabEnabled " id="ResetButton">Reset</li>
<li class="TabButton" id="BackButton">&lt;&lt;</li>
<li class="TabButton" id="PlayButton">Play</li>
<li class="TabButton" id="ForwButton">&gt;&gt;</li>
<li class="TabButton" id="CountButton">x</li>
</ul>




<script src="Flat Earth Dome Model_files\script.js"></script>



</div></div>

<script>
var SEL_GRP_AutoName1 = new Array('AutoName1ON','AutoName1OFF');
var SEL_GRPN_AutoName1 = new Array('AutoName1ON','AutoName1OFF');
</script>
<div class="OnOff SaveRestorePanel"><div id="SEL_ELE_AutoName1OFF" class="selele">
<div class="noprint"><div class="OnOffSign">+</div><a href="javascript:" onclick="SEL(SEL_GRP_AutoName1,[&#39;AutoName1ON&#39;],true);return false;"><span><div class="OnOffHead">Show Save-Restore Panel</div></span></a></div>
</div>

<div id="SEL_ELE_AutoName1ON" style="display:none;" class="selele">
<div class="noprint"><div class="OnOffSign">&#8722;</div><a href="javascript:" onclick="SEL(SEL_GRP_AutoName1,[&#39;AutoName1OFF&#39;],true);return false;"><span><div class="OnOffHead">Hide Save-Restore Panel</div></span></a></div><div class="OnOffText noborder">

<textarea id="SaveRestorePanel" name="SaveRestorePanel" spellcheck="false" class="ListingDisplay" rows="8" cols="60"></textarea>


<p>
<a href="javascript:void%20DataX.GetAppState(true)" title="JavaScript"><span class="lbtn">Get App State</span></a> <a href="javascript:void%20DataX.GetAppStateUrl(ThisPageUrl)" title="JavaScript"><span class="lbtn">Get App Url</span></a> <a href="javascript:void%20DataX.SetAppState()" title="JavaScript"><span class="lbtn lbtnGreen">Set App State</span></a> <a href="javascript:void%20DataX.ClearSaveRestoreDomObj()" title="JavaScript"><span class="lbtn lbtnRed">Clear</span></a>
</p>

</div>
</div>

</div>

</div>
</div>
</div>
</div>
</div>



<!-- Enhanced Planetary Motion System -->
<script src="Flat Earth Dome Model_files/planetary-overlay.js"></script>

</body></html>