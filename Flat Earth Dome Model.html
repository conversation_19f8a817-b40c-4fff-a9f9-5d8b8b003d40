<!DOCTYPE html>
<html lang="en" data-lt-installed="true"><head><meta http-equiv="Content-Type" content="text/html; charset=windows-1252">

<meta name="viewport" content="width=device-width, initial-scale=1.0">
<meta http-equiv="expires" content="0">
<meta http-equiv="imagetoolbar" content="no">
<meta name="robots" content="INDEX,FOLLOW">
<meta name="generator" content="paradome">
<meta name="publisher" content="Hussein ahmad">
<meta name="description" content="Here is an interactive Flat Earth Model that can show Sunrise, Sunset, Moonrise, Moonset, Moon Phases, Moon s apparent rotation, Sun s position on Equinox, Seasons, some aspects of Solar and Lunar Eclipses, Star trails, 24 hours Day Night at the Northpole and Antarctica, Celestial Poles, why people south of the equator can see the same Stars rotate clockwise around a singe celestial pole at the same time from different continents.">
<meta name="keywords" content="FlatEarth, Interactive, Knowlegde">

<title>Flat Earth Dome Model</title>

<script>
var ROOT              = '../';
var NUMBER_FORMATING  = 'en';
var ASP_PAGE          = 'index.asp';
var ASP_REF           = 'index.asp?page=Flat+Earth+Dome+Model&state=--690-10-11-10-189.9-2118.26968-30-10-10-11-11-10-20-10-31-2';
var PAGE_NAME         = 'Flat Earth Dome Model';
var PAGE_URL          = 'index.asp?page=Flat+Earth+Dome+Model';
var MEDIA_FOLDER      = '../blog/media/';
var WIKIDOC_URL       = '../wiki_doc/index.asp?page=PageName';
var PAGE_MODE         = '';
var MATH_PROCESSOR    = 'MathJax';
var ENABLE_EDIT       = true;
var HAS_FORMS         = false;
var IS_LOGED_IN       = false;
var IS_USER_LOGED_IN  = false;
var WIKI_HiliWordList   = [  ];
</script>

<script type="text/x-mathjax-config;executed=true">
MathJax.Hub.Config({
 displayAlign: 'left',
 displayIndent: '0',
 elements: ['Wiki'],
 preJax: '<MathJax>',
 postJax: '</MathJax>',
 tex2jax: {
  inlineMath: [],
  displayMath: [],
  balanceBraces: false
 },
});
</script>
<script async="" src="./Flat Earth Dome Model_files/MathJax.js.download"></script>
<script src="./Flat Earth Dome Model_files/wiki.js.download"></script>


<link rel="stylesheet" type="text/css" href="./Flat Earth Dome Model_files/styles.css" media="all">
<link rel="stylesheet" type="text/css" href="./Flat Earth Dome Model_files/style1.css" media="all">
</head>
<body id="body">
  <div style="visibility: hidden; overflow: hidden; position: absolute; top: 0px; height: 1px; width: auto; padding: 0px; border: 0px; margin: 0px; text-align: left; text-indent: 0px; text-transform: none; line-height: normal; letter-spacing: normal; word-spacing: normal;">
    <div id="MathJax_SVG_Hidden"></div>
    </div>

<div class="page notEditMode limitWidth" id="Layout-Root">
<div class="headerSection pageSection" id="Layout-HeaderSection">
<div class="pageFrame" id="Layout-HeaderFrame">
<div class="pageRow100">
<div class="brandingCol" id="Layout-BrandingPart">
<div class="branding" id="Layout-Branding">
<h1><a href="http://walter.bislins.ch/" title="Zur Homepage"><span>Wa</span>Bis</a></h1>
<h2>walter.bislins.ch</h2>
</div>
</div>
<div class="pagenavCol" id="Layout-NavigationPart">
<ul class="pagenav">
<li id="toNavigation"><a href="https://walter.bislins.ch/bloge/index.asp?page=Flat+Earth+Dome+Model&amp;state=--690-10-11-10-189.9-2118.26968-30-10-10-11-11-10-20-10-31-2#Layout-SidebarPart">Zur Navigation</a></li>
</ul>
</div>
<div class="searchformCol" id="Layout-SearchPart">
<ul id="toNavigationPicto" class="pagenav">
<li><a href="https://walter.bislins.ch/bloge/index.asp?page=Flat+Earth+Dome+Model&amp;state=--690-10-11-10-189.9-2118.26968-30-10-10-11-11-10-20-10-31-2#Layout-SidebarPart">Navi</a></li>
</ul>
<form action="https://walter.bislins.ch/bloge/index.asp?op=search" method="get" class="searchform" accept-charset="iso-8859-1">
<input type="hidden" name="op" value="search">
<input type="hidden" name="page" value="Flat Earth Dome Model">
<label for="searchField" class="label">Suche</label>
<input type="text" id="searchField" name="q" placeholder="Suche" accesskey="s" value="">
<input type="submit" name="submit" class="Button" value="Suche">
</form>
</div>
</div>
</div>
<div class="sitemenu" id="Layout-SiteMenubar">
<div class="menubar">
<!--?xml version="1.0" encoding="Windows-1252"?-->
<ul style="">
<li><a href="https://walter.bislins.ch/">Home</a></li>
<li><a href="https://walter.bislins.ch/blog/index.asp">Blog-De &#9662;</a>
  <ul style="display: none;">
    <li><a href="https://walter.bislins.ch/blog/index.asp">Neueste Artikel</a></li>
    <li><a href="https://walter.bislins.ch/blog/index.asp?blog=news">Neue Kommentare</a>
    </li><li><a href="https://walter.bislins.ch/blog/index.asp?page=Flat+Earth+Themen">Flat Earth Themen &#9656;</a>
      <ul style="display: none;">
        <li><a href="https://walter.bislins.ch/blog/index.asp?page=Flat%2DEarth%3A+Wie+stark+ist+die+Kr%FCmmung+der+Erde%3F">Wo ist die Kr�mmung?</a></li>
        <li><a href="https://walter.bislins.ch/blog/index.asp?page=K%F6nnen+wir+die+Abplattung+der+Erde+in+Satellitenbildern+sehen%3F">Abplattung der Erde</a></li>
        <li><a href="https://walter.bislins.ch/blog/index.asp?page=Das+Wahre+Gesicht+der+Erde%2C+Kameradistanz+spielt+eine+Rolle">Gesicht der Erde</a></li>
        <li><a href="https://walter.bislins.ch/blog/index.asp?page=Echtfarb%2DAufnahmen+der+Erde+vom+Satelliten+Himawari+der+JMA">Himawari 8</a></li>
        <li><a href="https://walter.bislins.ch/blog/index.asp?page=Zentrifugal%2D+und+Gravitationsbeschleunigung+in+einem+Flugzeug">Der E�tv�s Effekt</a></li>
      </ul>
    </li>
    <li><a href="https://walter.bislins.ch/blog/index.asp?blog=list&amp;tag=Aviatik">Aviatik &#9656;</a>
      <ul style="display: none;">
        <li><a href="https://walter.bislins.ch/blog/index.asp?page=Eine+Fluggeschwindigkeit%2C+verschiedene+Anzeigen">Fluggeschwindigkeiten</a></li>
        <li><a href="https://walter.bislins.ch/blog/index.asp?page=Rechner%3A+Umrechnen+von+Fluggeschwindigkeiten">Rechner: Fluggeschw.</a></li>
        <li><a href="https://walter.bislins.ch/blog/index.asp?page=Wie+bremst+ein+Verkehrsflugzeug+nach+der+Landung%3F">Wie bremst ein Flugzeug?</a></li>
        <li><a href="https://walter.bislins.ch/blog/index.asp?page=Kann+eine+Flugzeugt%FCr+im+Flug+ge%F6ffnet+werden%3F">T�r �ffnen im Flug?</a></li>
        <li><a href="https://walter.bislins.ch/blog/index.asp?page=Schub+und+Gegenschub+eines+Turbofan%2DTriebwerks">Schub eines Triebwerks</a></li>
        <li><a href="https://walter.bislins.ch/blog/index.asp?page=Compressibility+Correction+Chart%2C+Verwendung+und+Berechnung">Compressibility Corr. Chart</a></li>
        <li><a href="https://walter.bislins.ch/blog/index.asp?page=Aviatik+Faustformel%3A+Sinkrate+auf+dem+Gleitpfad">Faustformel: Sinkflug</a></li>
        <li><a href="https://walter.bislins.ch/blog/index.asp?page=Berechnung%3A+Mittlere+Aerodynamische+Fl%FCgeltiefe+%28MAC%29">Berechnung MAC</a></li>
      </ul>
    </li>
    <li><a href="https://walter.bislins.ch/blog/index.asp?blog=list&amp;tag=Mathematik">Mathematik</a></li>
    <li><a href="https://walter.bislins.ch/blog/index.asp?blog=list&amp;tag=Physik">Physik</a></li>
    <li><a href="https://walter.bislins.ch/blog/index.asp?blog=list&amp;tag=Programmierung">Programmierung</a></li>
    <li><a href="https://walter.bislins.ch/blog/index.asp?blog=list&amp;tag=Computer">Computer</a></li>
  </ul>
</li>
<li class="selected"><a href="https://walter.bislins.ch/bloge/index.asp">Blog-En &#9662;</a>
  <ul style="display: none;">
    <li><a href="https://walter.bislins.ch/bloge/index.asp">Latest Articles</a></li>
    <li><a href="https://walter.bislins.ch/bloge/index.asp?blog=news">New Comments</a></li>
    <li><a href="https://walter.bislins.ch/bloge/index.asp?page=Knowledge+Database">Knowledge Database</a></li>
    <li><a href="https://walter.bislins.ch/bloge/index.asp?page=Flat+Earth+Topics">Top Flat Earth Topics &#9656;</a>
      <ul style="display: none;">
        <li><a href="https://walter.bislins.ch/bloge/index.asp?page=Proof+of+Earth+Curvature%3A+The+Rainy+Lake+Experiment">Rainy Lake Experiment</a></li>
        <li><a href="https://walter.bislins.ch/bloge/index.asp?page=Finding+the+curvature+of+the+Earth">Where is the Curve?</a></li>
        <li><a href="https://walter.bislins.ch/bloge/index.asp?page=Comparison+of+Globe+and+Flat%2DEarth+Model+Predictions+with+Reality">Predictions and Reality</a></li>
        <li><a href="https://walter.bislins.ch/bloge/index.asp?page=Gravity+and+how+the+Heliocentric+Model+works">Gravity &amp; Heliocentric Model</a></li>
        <li><a href="https://walter.bislins.ch/bloge/index.asp?page=Simulation+of+Atmospheric+Refraction">Refraction Simualtor</a></li>
        <li><a href="https://walter.bislins.ch/bloge/index.asp?page=Flat+Earth+Dome+Model">FE Dome Model</a></li>
        <li><a href="https://walter.bislins.ch/bloge/index.asp?page=Distances+on+Globe+and+Flat+Earth">Distances Globe &amp; FE</a></li>
        <li><a href="https://walter.bislins.ch/bloge/index.asp?page=Creating+Flight+Plans+for+Flat+Earth">Flight Plans for FE</a></li>
        <li><a href="https://walter.bislins.ch/bloge/index.asp?page=Creating+an+Equinox+Sundial+made+of+Paper">Equinox Sundial</a></li>
      </ul>
    </li>
    <li><a href="https://walter.bislins.ch/bloge/index.asp?page=Calculators%2C+Equations+and+other+Stuff">Calculators &#9656;</a>
      <ul style="display: none;">
        <li><a href="https://walter.bislins.ch/bloge/index.asp?page=Advanced+Earth+Curvature+Calculator">Earth Curve Calculator</a></li>
        <li><a href="https://walter.bislins.ch/bloge/index.asp?page=Calculators+and+Units+Converters">Untis &amp; other Calculators</a></li>
        <li><a href="https://walter.bislins.ch/bloge/index.asp?page=Distances+on+Globe+and+Flat+Earth">Distances Globe &amp; FE</a></li>
        <li><a href="https://walter.bislins.ch/bloge/index.asp?page=Earth+Gravity+Calculator">Gravity Calculator</a></li>
        <li><a href="https://walter.bislins.ch/bloge/index.asp?page=Rainy+Lake+Experiment%3A+WGS84+Calculator">WGS84 Calculator</a></li>
        <li><a href="https://walter.bislins.ch/bloge/index.asp?page=Centrifugal+and+Gravitational+Acceleration+in+an+Aircraft">E�tv�s Effect</a></li>
        <li><a href="https://walter.bislins.ch/bloge/index.asp?page=Calculators%2C+Equations+and+other+Stuff">Calculators, Equations...</a></li>
      </ul>
    </li>
  </ul>
</li>
<li><a href="https://walter.bislins.ch/musik/index.asp">Musik &#9662;</a>
  <ul style="display: none;">
    <li><a href="https://walter.bislins.ch/musik/index.asp?page=Meine+Musik%2DVideos">Meine Musik Videos</a></li>
    <li><a href="https://walter.bislins.ch/musik/index.asp?page=Meine+CD%27s">Meine Musik CD's</a></li>
    <li><a href="https://walter.bislins.ch/musik/index.asp?page=Meine+Instrumente">Instrumente</a></li>
    <li><a href="https://walter.bislins.ch/musik/index.asp?page=Musikalische+Laufbahn">Musikalische Laufbahn</a></li>
  </ul>
</li>
<li><a href="https://walter.bislins.ch/wissen/index.asp">Wissen &#9662;</a>
  <ul style="display: none;">
    <li><a href="https://walter.bislins.ch/physik/index.asp">Mathe &amp; Physik</a></li>
    <li><a href="https://walter.bislins.ch/physik/index.asp?page=Struktur+der+physikalischen+Gesetze">Prinzip der kleinsten Wirkung</a></li>
    <li><a href="https://walter.bislins.ch/bloge/index.asp?page=Derivation+of+Special+Relativity+from+basic+observable+Principles">Special Relativity derived</a></li>
    <li><a href="https://walter.bislins.ch/physik/index.asp?page=Allgemeine+Relativit%E4tstheorie">Allg. Relativit�tstheorie</a></li>
    <li><a href="https://walter.bislins.ch/aviatik/index.asp">Aviatik-Wiki</a></li>
    <li><a href="https://walter.bislins.ch/aviatik/index.asp?page=Grundlagen">Aviatik Grundlagen</a></li>
    <li><a href="https://walter.bislins.ch/fsim/index.asp">Flugsimulation</a></li>
  </ul>
</li>
<li><a href="https://walter.bislins.ch/projekte/index.asp">Projekte &#9662;</a>
  <ul style="display: none;">
    <li><a href="https://walter.bislins.ch/projekte/index.asp?page=Sudoku">Sudoku</a></li>
    <li><a href="https://walter.bislins.ch/projekte/index.asp?page=JavaScript%2DProjekte">JavaScripts &#9662;</a>
      <ul style="display: none;">
        <li><a href="https://walter.bislins.ch/projekte/index.asp?page=JavaScripts%3A+Graph+%28JSG%29">Graph</a>
        </li><li><a href="https://walter.bislins.ch/projekte/index.asp?page=JavaScripts%3A+3D%2DGraphX+%28JSGX3D%29">3D-GraphX</a>
        </li><li><a href="https://walter.bislins.ch/projekte/index.asp?page=JavaScripts%3A+3D%2DGraph+%28JSG3D%29">3D-Graph</a>
        </li><li><a href="https://walter.bislins.ch/projekte/index.asp?page=JavaScripts%3A+ControlPanel+%28CP%29">ControlPanel</a>
        </li><li><a href="https://walter.bislins.ch/projekte/index.asp?page=JavaScripts%3A+Nullstellensuche+mit+Newton%2DVerfahren">Newton Solver</a>
        </li><li><a href="https://walter.bislins.ch/projekte/index.asp?page=JavaScripts%3A+Async">Async</a>
        </li><li><a href="https://walter.bislins.ch/projekte/index.asp?page=JavaScripts%3A+Sim">Sim</a>
        </li><li><a href="https://walter.bislins.ch/projekte/index.asp?page=JavaScripts%3A+EarthMap">EarthMap</a>
        </li><li><a href="https://walter.bislins.ch/projekte/index.asp?page=JavaScripts%3A+Animator+%28ANIM%29">Animator</a>
        </li><li><a href="https://walter.bislins.ch/projekte/index.asp?page=Diverse+JavaScripts">Diverse</a>
      </li></ul>
    </li>
    <li><a href="https://walter.bislins.ch/projekte/index.asp?page=ASP%2DProjekte">ASP-Module</a></li>
    <li><a href="https://walter.bislins.ch/wiki_doc/index.asp">Wiki Doku</a></li>
  </ul>
</li>
<li><a href="https://walter.bislins.ch/gallery/index.asp">Galerien &#9662;</a>
  <ul style="display: none;">
    <li><a href="https://walter.bislins.ch/gallery/toepfer.asp">s'T�pferh�sli Watt</a></li>
    <li><a href="https://walter.bislins.ch/gallery/pfuedi.asp">Unsere Katze Pf�di</a></li>
    <li><a href="https://walter.bislins.ch/gallery/index.asp?page=Ausbau+der+Scheune">Ausbau der Scheune</a></li>
    <li><a href="https://walter.bislins.ch/gallery/index.asp?page=Lithographien+Fredi+Br%E4ndli">Lithographien Fredi Br�ndli</a></li>
  </ul>
</li>
<li><a href="https://walter.bislins.ch/walti/index.asp">Pers�nliches &#9662;</a>
  <ul style="display: none;">
    <li><a href="https://walter.bislins.ch/walti/index.asp">�ber mich</a></li>
    <li><a href="https://walter.bislins.ch/angst/index.asp">Angst &amp; Depression</a></li>
    <li><a href="https://walter.bislins.ch/work/index.asp?page=Medien%2C+die+extern+referenziert+werden" rel="nofollow">Public Media</a></li>
    <li><a href="https://walter.bislins.ch/work/index.asp" rel="nofollow">Arbeitsbereich</a></li>
  </ul>
</li>
</ul>

</div>
</div>
<div class="headertext" style="background: url(../blog/media/bgblog.jpg) repeat-x;" id="Layout-HeaderTextAndImage">
<h2><a href="https://walter.bislins.ch/bloge/index.asp" title="Walter Bislin&#39;s Blog-En" style="color: #dbad89;">Walter Bislin's Blog-En</a></h2>
</div>
</div>
<div class="contentSection pageSection" id="Layout-MainSection">
<div class="pageFrame" id="Layout-MainFrame">
<div class="pageRow100">
<div id="SidebarOnOffButtons">
<div id="SidebarOffButton" class="sidebarOnOffButton">
<a href="javascript:LayoutMaximize()">�</a></div>
<div id="SidebarOnButton" class="sidebarOnOffButton hide">
<a href="javascript:LayoutNormal()">�</a></div>
</div>
<div id="FullscreenOnOffButtons">
<div id="FullscreenOffButton" class="sidebarOnOffButton hide">
<a href="javascript:LayoutFullscreenOff()">[&#8201;&#8722;&#8201;]</a></div>
<div id="FullscreenOnButton" class="sidebarOnOffButton">
<a href="javascript:LayoutFullscreenOn()">[&#8201;+&#8201;]</a></div>
</div>
<div class="layoutCol75" id="Layout-ContentPart">


<script src="./Flat Earth Dome Model_files/jsg.js.download"></script>


<script src="./Flat Earth Dome Model_files/jsgx3d.js.download"></script>

<script src="./Flat Earth Dome Model_files/jsgMouseHandler.js.download"></script>

<script src="./Flat Earth Dome Model_files/EarthMap.js.download"></script>

<script src="./Flat Earth Dome Model_files/NumFormatter.js.download"></script>
<script src="./Flat Earth Dome Model_files/Slider.js.download"></script>
<script src="./Flat Earth Dome Model_files/ControlPanel.js.download"></script>

<script src="./Flat Earth Dome Model_files/Tabs.js.download"></script>

<script src="./Flat Earth Dome Model_files/DataX.js.download"></script>

<script src="./Flat Earth Dome Model_files/xtc.js.download"></script>

<script src="./Flat Earth Dome Model_files/ModelAnimation.js.download"></script>

<ul id="DomeDemoTabs" class="TabSelectors Top TopMargin">
<li class="TabButton TabEnabled " id="IntroButton">Intro</li>
<li class="TabButton TabEnabled " id="EclipsesButton">Eclipses</li>
<li class="TabButton TabEnabled " id="EquinoxButton">Equinox</li>
<li class="TabButton TabEnabled " id="DayNightButton">DayNight</li>
<li class="TabButton TabEnabled " id="PolesButton">Poles</li>
<li class="TabButton TabEnabled " id="StarsButton">Stars</li>
<li class="TabButton TabEnabled " id="ResetButton">Reset</li>
<li class="TabButton" id="BackButton">&lt;&lt;</li>
<li class="TabButton" id="PlayButton">Play</li>
<li class="TabButton" id="ForwButton">&gt;&gt;</li>
<li class="TabButton" id="CountButton">x</li>
</ul>
<!-- <table id="OptionPanel" class="ControlPanel NCols2 InputNormalWidth"><tbody><tr class="Row1"><td class="Label Col1"><div class="FieldText">Show</div></td><td colspan="3" class="Value Col2"><table class="FieldGrid"><tbody><tr><td style="width:20%;" class="FieldCell" id="OptionPanel-ShowFeGrid-Field"><div class="FieldText"><input type="checkbox" id="OptionPanel-ShowFeGrid" name="OptionPanel-ShowFeGrid" class="CheckBox" value="ShowFeGrid"><span class="FieldCaption">FE Grid&nbsp;</span></div></td><td style="width:20%;" class="FieldCell" id="OptionPanel-ShowDomeGrid-Field"><div class="FieldText"><input type="checkbox" id="OptionPanel-ShowDomeGrid" name="OptionPanel-ShowDomeGrid" class="CheckBox" value="ShowDomeGrid"><span class="FieldCaption">Dome Grid&nbsp;</span></div></td><td style="width:20%;" class="FieldCell" id="OptionPanel-ShowShadow-Field"><div class="FieldText"><input type="checkbox" id="OptionPanel-ShowShadow" name="OptionPanel-ShowShadow" class="CheckBox" value="ShowShadow"><span class="FieldCaption">Shadow&nbsp;</span></div></td><td style="width:20%;" class="FieldCell" id="OptionPanel-ShowSunTrack-Field"><div class="FieldText"><input type="checkbox" id="OptionPanel-ShowSunTrack" name="OptionPanel-ShowSunTrack" class="CheckBox" value="ShowSunTrack"><span class="FieldCaption">Sun Track&nbsp;</span></div></td><td style="width:20%;" class="FieldCell" id="OptionPanel-ShowMoonTrack-Field"><div class="FieldText"><input type="checkbox" id="OptionPanel-ShowMoonTrack" name="OptionPanel-ShowMoonTrack" class="CheckBox" value="ShowMoonTrack"><span class="FieldCaption">Moon Track&nbsp;</span></div></td></tr><tr><td class="FieldCell Disabled" id="OptionPanel-ShowSphere-Field"><div class="FieldText"><input type="checkbox" id="OptionPanel-ShowSphere" name="OptionPanel-ShowSphere" class="CheckBox" value="ShowSphere"><span class="FieldCaption">Sphere&nbsp;</span></div></td><td class="FieldCell Disabled" id="OptionPanel-ShowStars-Field"><div class="FieldText"><input type="checkbox" id="OptionPanel-ShowStars" name="OptionPanel-ShowStars" class="CheckBox" value="ShowStars"><span class="FieldCaption">Stars&nbsp;</span></div></td><td class="FieldCell Disabled" id="OptionPanel-ShowDomeRays-Field"><div class="FieldText"><input type="checkbox" id="OptionPanel-ShowDomeRays" name="OptionPanel-ShowDomeRays" class="CheckBox" value="ShowDomeRays"><span class="FieldCaption">Dome Rays&nbsp;</span></div></td><td class="FieldCell Disabled" id="OptionPanel-ShowSphereRays-Field"><div class="FieldText"><input type="checkbox" id="OptionPanel-ShowSphereRays" name="OptionPanel-ShowSphereRays" class="CheckBox" value="ShowSphereRays"><span class="FieldCaption">Sphere Rays&nbsp;</span></div></td><td class="FieldCell" id="OptionPanel-ShowManyRays-Field"><div class="FieldText"><input type="checkbox" id="OptionPanel-ShowManyRays" name="OptionPanel-ShowManyRays" class="CheckBox" value="ShowManyRays"><span class="FieldCaption">Many Rays&nbsp;</span></div></td></tr></tbody></table></td></tr><tr class="Row2"><td class="Label Col1"><div class="FieldText">RayTarget</div></td><td class="Value Col2">
  <table class="FieldGrid"><tbody><tr><td style="width:50%;" class="FieldCell" id="OptionPanel-RayTarget-0-Field"><div class="FieldText"><input type="radio" id="OptionPanel-RayTarget-0" name="OptionPanel-RayTarget" class="Radio" value="0"><span class="FieldCaption">Observer&nbsp;</span></div></td><td style="width:50%;" class="FieldCell" id="OptionPanel-RayTarget-1-Field"><div class="FieldText"><input type="radio" id="OptionPanel-RayTarget-1" name="OptionPanel-RayTarget" class="Radio" value="1"><span class="FieldCaption">FlatEarth&nbsp;</span></div></td></tr></tbody></table></td><td class="Label Col3"><div class="FieldText">RaySource</div></td><td class="Value Col4"><table class="FieldGrid"><tbody><tr><td style="width:33%;" class="FieldCell" id="OptionPanel-RaySource-0-Field"><div class="FieldText"><input type="radio" id="OptionPanel-RaySource-0" name="OptionPanel-RaySource" class="Radio" value="0"><span class="FieldCaption">Sun&nbsp;</span></div></td><td style="width:33%;" class="FieldCell" id="OptionPanel-RaySource-1-Field"><div class="FieldText"><input type="radio" id="OptionPanel-RaySource-1" name="OptionPanel-RaySource" class="Radio" value="1"><span class="FieldCaption">Moon&nbsp;</span></div></td><td style="width:33%;" class="FieldCell" id="OptionPanel-RaySource-2-Field"><div class="FieldText"><input type="radio" id="OptionPanel-RaySource-2" name="OptionPanel-RaySource" class="Radio" value="2"><span class="FieldCaption">Star&nbsp;</span></div></td></tr></tbody></table></td></tr></tbody></table> -->





<script src="Flat Earth Dome Model_files\script.js"></script>



</div></div>

<script>
var SEL_GRP_AutoName1 = new Array('AutoName1ON','AutoName1OFF');
var SEL_GRPN_AutoName1 = new Array('AutoName1ON','AutoName1OFF');
</script>
<div class="OnOff SaveRestorePanel"><div id="SEL_ELE_AutoName1OFF" class="selele">
<div class="noprint"><div class="OnOffSign">+</div><a href="javascript:" onclick="SEL(SEL_GRP_AutoName1,[&#39;AutoName1ON&#39;],true);return false;"><span><div class="OnOffHead">Show Save-Restore Panel</div></span></a></div>
</div>

<div id="SEL_ELE_AutoName1ON" style="display:none;" class="selele">
<div class="noprint"><div class="OnOffSign">&#8722;</div><a href="javascript:" onclick="SEL(SEL_GRP_AutoName1,[&#39;AutoName1OFF&#39;],true);return false;"><span><div class="OnOffHead">Hide Save-Restore Panel</div></span></a></div><div class="OnOffText noborder">

<textarea id="SaveRestorePanel" name="SaveRestorePanel" spellcheck="false" class="ListingDisplay" rows="8" cols="60"></textarea>


<p>
<a href="javascript:void%20DataX.GetAppState(true)" title="JavaScript"><span class="lbtn">Get App State</span></a> <a href="javascript:void%20DataX.GetAppStateUrl(ThisPageUrl)" title="JavaScript"><span class="lbtn">Get App Url</span></a> <a href="javascript:void%20DataX.SetAppState()" title="JavaScript"><span class="lbtn lbtnGreen">Set App State</span></a> <a href="javascript:void%20DataX.ClearSaveRestoreDomObj()" title="JavaScript"><span class="lbtn lbtnRed">Clear</span></a>
</p>

</div>
</div>

</div>

</div>
</div>
</div>
</div>
</div>



</body></html>