/**
 * Enhanced Planetary Motion System for Flat Earth Dome Model
 * Implements realistic planetary motion with retrograde behavior
 */

// Planetary Motion System
var PlanetaryMotion = {
    // Planet definitions with orbital parameters
    planets: {
        mercury: {
            name: 'Mercury',
            color: '#8C7853',
            glowColor: '#B8A082',
            size: 4,
            orbitalPeriod: 87.97, // days
            synodicPeriod: 115.88, // days (for retrograde calculation)
            meanDistance: 0.387, // AU from Sun
            eccentricity: 0.206,
            inclination: 7.0, // degrees
            longitudeOfAscendingNode: 48.3,
            argumentOfPeriapsis: 29.1,
            meanAnomalyAtEpoch: 174.8,
            visible: true,
            showOrbit: false,
            brightness: 0.8,
            retrogradeColor: '#FF6B6B',
            description: 'Fastest planet, closest to Sun'
        },
        venus: {
            name: 'Venus',
            color: '#FFC649',
            glowColor: '#FFD700',
            size: 6,
            orbitalPeriod: 224.70,
            synodicPeriod: 583.92,
            meanDistance: 0.723,
            eccentricity: 0.007,
            inclination: 3.4,
            longitudeOfAscendingNode: 76.7,
            argumentOfPeriapsis: 54.9,
            meanAnomalyAtEpoch: 50.1,
            visible: true,
            showOrbit: false,
            brightness: 1.2,
            retrogradeColor: '#FF8C42',
            description: 'Brightest planet, Morning/Evening Star'
        },
        mars: {
            name: 'Mars',
            color: '#CD5C5C',
            glowColor: '#FF6B6B',
            size: 5,
            orbitalPeriod: 686.98,
            synodicPeriod: 779.94,
            meanDistance: 1.524,
            eccentricity: 0.093,
            inclination: 1.9,
            longitudeOfAscendingNode: 49.6,
            argumentOfPeriapsis: 286.5,
            meanAnomalyAtEpoch: 19.4,
            visible: true,
            showOrbit: false,
            brightness: 0.9,
            retrogradeColor: '#FF4444',
            description: 'Red Planet, shows prominent retrograde loops'
        },
        jupiter: {
            name: 'Jupiter',
            color: '#D8CA9D',
            glowColor: '#E6D7B8',
            size: 8,
            orbitalPeriod: 4332.59,
            synodicPeriod: 398.88,
            meanDistance: 5.204,
            eccentricity: 0.049,
            inclination: 1.3,
            longitudeOfAscendingNode: 100.5,
            argumentOfPeriapsis: 273.9,
            meanAnomalyAtEpoch: 20.0,
            visible: true,
            showOrbit: false,
            brightness: 1.0,
            retrogradeColor: '#FFB347',
            description: 'Largest planet, King of Planets'
        },
        saturn: {
            name: 'Saturn',
            color: '#FAD5A5',
            glowColor: '#FFEBB3',
            size: 7,
            orbitalPeriod: 10759.22,
            synodicPeriod: 378.09,
            meanDistance: 9.537,
            eccentricity: 0.057,
            inclination: 2.5,
            longitudeOfAscendingNode: 113.7,
            argumentOfPeriapsis: 339.4,
            meanAnomalyAtEpoch: 317.0,
            visible: true,
            showOrbit: false,
            brightness: 0.7,
            retrogradeColor: '#DEB887',
            description: 'Ringed planet, Lord of the Rings'
        }
    },

    // Configuration
    config: {
        showPlanets: true,
        showRetrograde: true,
        planetarySpeed: 1.0, // Speed multiplier for planetary motion
        retrogradeHighlight: true,
        orbitOpacity: 0.3
    },

    // Initialize the planetary system
    init: function() {
        this.epochDate = new Date('2000-01-01T12:00:00Z'); // J2000.0 epoch
        this.calculateInitialPositions();
    },

    // Calculate initial planetary positions
    calculateInitialPositions: function() {
        for (var planetName in this.planets) {
            var planet = this.planets[planetName];
            planet.currentPosition = this.calculatePlanetPosition(planet, 0);
            planet.retrogradePhase = 0; // 0 = normal, 1 = approaching retrograde, 2 = retrograde, 3 = leaving retrograde
            planet.apparentMotion = 1; // 1 = prograde, -1 = retrograde
        }
    },

    // Calculate planet position for given time offset (in days from epoch)
    calculatePlanetPosition: function(planet, timeOffset) {
        // Mean anomaly calculation
        var meanAnomaly = planet.meanAnomalyAtEpoch + (360 * timeOffset / planet.orbitalPeriod);
        meanAnomaly = this.normalizeAngle(meanAnomaly);

        // Solve Kepler's equation for eccentric anomaly (iterative method)
        var eccentricAnomaly = this.solveKeplerEquation(meanAnomaly, planet.eccentricity);

        // True anomaly calculation
        var trueAnomaly = this.calculateTrueAnomaly(eccentricAnomaly, planet.eccentricity);

        // Distance from sun (heliocentric distance)
        var distance = planet.meanDistance * (1 - planet.eccentricity * Math.cos(ToRad(eccentricAnomaly)));

        // Position in orbital plane
        var x = distance * Math.cos(ToRad(trueAnomaly + planet.argumentOfPeriapsis));
        var y = distance * Math.sin(ToRad(trueAnomaly + planet.argumentOfPeriapsis));

        // Apply orbital inclination and node rotation (3D transformation)
        var cosI = Math.cos(ToRad(planet.inclination));
        var sinI = Math.sin(ToRad(planet.inclination));
        var cosO = Math.cos(ToRad(planet.longitudeOfAscendingNode));
        var sinO = Math.sin(ToRad(planet.longitudeOfAscendingNode));
        var cosW = Math.cos(ToRad(planet.argumentOfPeriapsis));
        var sinW = Math.sin(ToRad(planet.argumentOfPeriapsis));

        // Transform to heliocentric coordinates
        var xHelio = x * (cosO * cosW - sinO * sinW * cosI) - y * (cosO * sinW + sinO * cosW * cosI);
        var yHelio = x * (sinO * cosW + cosO * sinW * cosI) - y * (sinO * sinW - cosO * cosW * cosI);
        var zHelio = x * (sinW * sinI) + y * (cosW * sinI);

        return {
            heliocentric: { x: xHelio, y: yHelio, z: zHelio },
            distance: distance,
            trueAnomaly: trueAnomaly,
            meanAnomaly: meanAnomaly,
            eccentricAnomaly: eccentricAnomaly
        };
    },

    // Solve Kepler's equation using Newton-Raphson method
    solveKeplerEquation: function(meanAnomaly, eccentricity) {
        var M = ToRad(meanAnomaly);
        var e = eccentricity;
        var E = M; // Initial guess
        var tolerance = 1e-8;
        var maxIterations = 10;

        for (var i = 0; i < maxIterations; i++) {
            var f = E - e * Math.sin(E) - M;
            var fp = 1 - e * Math.cos(E);
            var deltaE = f / fp;
            E = E - deltaE;

            if (Math.abs(deltaE) < tolerance) break;
        }

        return ToDeg(E);
    },

    // Calculate true anomaly from eccentric anomaly
    calculateTrueAnomaly: function(eccentricAnomaly, eccentricity) {
        var E = ToRad(eccentricAnomaly);
        var e = eccentricity;

        var cosNu = (Math.cos(E) - e) / (1 - e * Math.cos(E));
        var sinNu = (Math.sqrt(1 - e * e) * Math.sin(E)) / (1 - e * Math.cos(E));

        var nu = Math.atan2(sinNu, cosNu);
        return ToDeg(nu);
    },

    // Calculate retrograde motion with enhanced accuracy
    calculateRetrogradeMotion: function(planet, earthPosition, timeOffset) {
        // Calculate positions at multiple time points for better accuracy
        var timeStep = 0.5; // Half day steps for better precision
        var pastPos = this.calculatePlanetPosition(planet, timeOffset - timeStep);
        var currentPos = this.calculatePlanetPosition(planet, timeOffset);
        var futurePos = this.calculatePlanetPosition(planet, timeOffset + timeStep);

        // Calculate Earth's position (for flat earth model, Earth is stationary at origin)
        var earthPos = { x: 0, y: 0, z: 0 };

        // Calculate geocentric positions
        var pastGeo = this.heliocentricToGeocentric(pastPos.heliocentric, earthPos);
        var currentGeo = this.heliocentricToGeocentric(currentPos.heliocentric, earthPos);
        var futureGeo = this.heliocentricToGeocentric(futurePos.heliocentric, earthPos);

        // Calculate apparent motion vectors
        var pastToCurrentMotion = this.calculateApparentLongitudeChange(pastGeo, currentGeo);
        var currentToFutureMotion = this.calculateApparentLongitudeChange(currentGeo, futureGeo);

        // Determine retrograde state based on motion direction
        var isRetrograde = (pastToCurrentMotion < 0) || (currentToFutureMotion < 0);

        // Calculate retrograde phase with more nuance
        var retrogradePhase = 0; // 0 = normal prograde
        if (pastToCurrentMotion > 0 && currentToFutureMotion < 0) {
            retrogradePhase = 1; // Approaching retrograde
        } else if (pastToCurrentMotion < 0 && currentToFutureMotion < 0) {
            retrogradePhase = 2; // Full retrograde
        } else if (pastToCurrentMotion < 0 && currentToFutureMotion > 0) {
            retrogradePhase = 3; // Leaving retrograde
        }

        // Calculate synodic position for retrograde timing
        var synodicAngle = this.calculateSynodicPosition(planet, timeOffset);

        // Enhanced retrograde detection based on synodic period
        var synodicRetrograde = this.isSynodicRetrograde(planet, synodicAngle);

        planet.apparentMotion = isRetrograde ? -1 : 1;
        planet.retrogradePhase = retrogradePhase;
        planet.synodicAngle = synodicAngle;

        return {
            position: currentPos,
            geocentric: currentGeo,
            isRetrograde: isRetrograde || synodicRetrograde,
            retrogradePhase: retrogradePhase,
            synodicAngle: synodicAngle,
            motionRate: currentToFutureMotion
        };
    },

    // Calculate synodic position (relative to Earth-Sun line)
    calculateSynodicPosition: function(planet, timeOffset) {
        var planetPos = this.calculatePlanetPosition(planet, timeOffset);
        var earthAngle = 360 * timeOffset / 365.25; // Earth's position
        var planetAngle = Math.atan2(planetPos.heliocentric.y, planetPos.heliocentric.x) * 180 / Math.PI;

        var synodicAngle = planetAngle - earthAngle;
        return this.normalizeAngle(synodicAngle);
    },

    // Determine if planet is in synodic retrograde phase
    isSynodicRetrograde: function(planet, synodicAngle) {
        // For superior planets (Mars, Jupiter, Saturn), retrograde occurs around opposition
        if (planet.meanDistance > 1.0) {
            return Math.abs(synodicAngle - 180) < 60; // ±60° around opposition
        }
        // For inferior planets (Mercury, Venus), retrograde occurs around inferior conjunction
        else {
            return Math.abs(synodicAngle) < 30 || Math.abs(synodicAngle - 360) < 30; // ±30° around conjunction
        }
    },

    // Convert heliocentric to geocentric coordinates
    heliocentricToGeocentric: function(planetHelio, earthHelio) {
        return {
            x: planetHelio.x - earthHelio.x,
            y: planetHelio.y - earthHelio.y,
            z: planetHelio.z - earthHelio.z
        };
    },

    // Calculate apparent longitude change
    calculateApparentLongitudeChange: function(pos1, pos2) {
        var lon1 = Math.atan2(pos1.y, pos1.x);
        var lon2 = Math.atan2(pos2.y, pos2.x);
        var delta = lon2 - lon1;
        
        // Normalize to [-π, π]
        while (delta > Math.PI) delta -= 2 * Math.PI;
        while (delta < -Math.PI) delta += 2 * Math.PI;
        
        return delta;
    },

    // Convert planetary position to dome coordinates
    planetToDomeCoord: function(planetPosition, earthPosition) {
        // Convert to geocentric coordinates
        var geocentric = this.heliocentricToGeocentric(planetPosition.heliocentric, earthPosition);
        
        // Convert to celestial coordinates (simplified)
        var distance = Math.sqrt(geocentric.x * geocentric.x + geocentric.y * geocentric.y + geocentric.z * geocentric.z);
        var lat = Math.asin(geocentric.z / distance) * 180 / Math.PI;
        var lng = Math.atan2(geocentric.y, geocentric.x) * 180 / Math.PI;

        // Use the existing dome coordinate system from FeDomeApp
        return FeDomeApp.CelestLatLongToDomeCoord(lat, lng);
    },

    // Normalize angle to 0-360 degrees
    normalizeAngle: function(angle) {
        while (angle < 0) angle += 360;
        while (angle >= 360) angle -= 360;
        return angle;
    },

    // Update all planetary positions
    updatePlanets: function(timeOffset) {
        // Calculate Earth's position (assuming Earth is at origin for flat earth model)
        var earthPosition = { x: 0, y: 0, z: 0 };

        for (var planetName in this.planets) {
            var planet = this.planets[planetName];
            if (planet.visible) {
                var motion = this.calculateRetrogradeMotion(planet, earthPosition, timeOffset);
                planet.currentPosition = motion.position;
                planet.geocentricPosition = motion.geocentric;
                planet.isRetrograde = motion.isRetrograde;
                planet.retrogradePhase = motion.retrogradePhase;
                planet.domeCoord = this.planetToDomeCoord(motion.position, earthPosition);
            }
        }
    },

    // Get planet by name
    getPlanet: function(name) {
        return this.planets[name.toLowerCase()];
    },

    // Toggle planet visibility
    togglePlanet: function(planetName, visible) {
        var planet = this.getPlanet(planetName);
        if (planet) {
            planet.visible = visible;
        }
    },

    // Toggle orbit visibility
    toggleOrbit: function(planetName, visible) {
        var planet = this.getPlanet(planetName);
        if (planet) {
            planet.showOrbit = visible;
        }
    }
};

// Initialize the planetary motion system
PlanetaryMotion.init();
