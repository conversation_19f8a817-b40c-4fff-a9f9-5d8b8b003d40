/**
 * Planetary Overlay System for Flat Earth Dome Model
 * This system adds planetary motion without modifying existing JavaScript logic
 */

// Planetary Overlay System - completely independent
window.PlanetaryOverlay = {
    // Configuration
    enabled: true,
    showPlanets: true,
    showRetrograde: true,
    planetarySpeed: 1.0,
    
    // Planet data with simplified orbital parameters
    planets: {
        mercury: {
            name: 'Mercury',
            color: '#8C7853',
            size: 4,
            orbitalPeriod: 88, // days
            distance: 0.4, // relative distance
            currentAngle: 0,
            retrograde: false,
            visible: true
        },
        venus: {
            name: 'Venus',
            color: '#FFC649',
            size: 6,
            orbitalPeriod: 225,
            distance: 0.7,
            currentAngle: 45,
            retrograde: false,
            visible: true
        },
        mars: {
            name: 'Mars',
            color: '#CD5C5C',
            size: 5,
            orbitalPeriod: 687,
            distance: 1.5,
            currentAngle: 90,
            retrograde: false,
            visible: true
        },
        jupiter: {
            name: 'Jupiter',
            color: '#D8CA9D',
            size: 8,
            orbitalPeriod: 4333,
            distance: 5.2,
            currentAngle: 135,
            retrograde: false,
            visible: true
        },
        saturn: {
            name: '<PERSON>',
            color: '#FAD5A5',
            size: 7,
            orbitalPeriod: 10759,
            distance: 9.5,
            currentAngle: 180,
            retrograde: false,
            visible: true
        }
    },
    
    // Initialize the system
    init: function() {
        console.log('Planetary Overlay System initialized');
        console.log('FeDomeApp available:', typeof FeDomeApp !== 'undefined');
        console.log('FeDomeApp.IsInit:', typeof FeDomeApp !== 'undefined' ? FeDomeApp.IsInit : 'N/A');
        console.log('FeDomeApp.GraphObject:', typeof FeDomeApp !== 'undefined' ? !!FeDomeApp.GraphObject : 'N/A');

        this.startTime = Date.now();
        this.lastUpdate = this.startTime;

        // Add button handler
        this.addPlanetsButtonHandler();

        // Start the animation loop
        this.animate();

        console.log('Planetary system ready. Click the "Planets" button to enable planetary motion.');
    },
    
    // Animation loop
    animate: function() {
        if (this.enabled && this.showPlanets && typeof FeDomeApp !== 'undefined' && FeDomeApp.IsInit) {
            this.updatePlanets();
            this.drawPlanets();
        }

        // Continue animation
        requestAnimationFrame(() => this.animate());
    },
    
    // Update planet positions
    updatePlanets: function() {
        const currentTime = Date.now();
        const deltaTime = (currentTime - this.lastUpdate) / 1000; // seconds
        this.lastUpdate = currentTime;
        
        // Update each planet's position
        for (let planetName in this.planets) {
            const planet = this.planets[planetName];
            if (planet.visible) {
                // Simple orbital motion
                const angularSpeed = (360 / planet.orbitalPeriod) * this.planetarySpeed * deltaTime / 86400; // degrees per second
                planet.currentAngle += angularSpeed;
                planet.currentAngle = planet.currentAngle % 360;
                
                // Simple retrograde detection (simplified)
                planet.retrograde = this.calculateRetrograde(planet);
            }
        }
    },
    
    // Simple retrograde calculation
    calculateRetrograde: function(planet) {
        // For outer planets, retrograde occurs around opposition
        if (planet.distance > 1.0) {
            const oppositionAngle = 180;
            const angleDiff = Math.abs(planet.currentAngle - oppositionAngle);
            return angleDiff < 30; // ±30° around opposition
        }
        // For inner planets, retrograde occurs around inferior conjunction
        else {
            const conjunctionAngle = 0;
            const angleDiff = Math.min(Math.abs(planet.currentAngle - conjunctionAngle), 
                                     Math.abs(planet.currentAngle - 360));
            return angleDiff < 15; // ±15° around conjunction
        }
    },
    
    // Draw planets on the existing canvas
    drawPlanets: function() {
        // Try to get the existing graphics context
        if (typeof FeDomeApp !== 'undefined' && FeDomeApp.GraphObject && FeDomeApp.IsInit) {
            try {
                const g = FeDomeApp.GraphObject;

                // Save current graphics state
                g.SetAlpha(1);

                // Draw each visible planet
                for (let planetName in this.planets) {
                    const planet = this.planets[planetName];
                    if (planet.visible) {
                        this.drawPlanet(g, planet);
                    }
                }
            } catch (e) {
                // Silently handle drawing errors
                console.warn('Error in drawPlanets:', e);
            }
        } else {
            // Fallback: create a simple HTML overlay if main graphics aren't available
            this.drawPlanetsHTML();
        }
    },

    // Fallback HTML drawing method
    drawPlanetsHTML: function() {
        let overlay = document.getElementById('planetaryHTMLOverlay');
        if (!overlay) {
            overlay = document.createElement('div');
            overlay.id = 'planetaryHTMLOverlay';
            overlay.style.cssText = `
                position: fixed;
                top: 100px;
                left: 10px;
                background: rgba(0, 0, 0, 0.7);
                color: white;
                padding: 10px;
                border-radius: 5px;
                font-family: Arial, sans-serif;
                font-size: 12px;
                z-index: 999;
                max-width: 200px;
            `;
            document.body.appendChild(overlay);
        }

        let html = '<div style="font-weight: bold; margin-bottom: 5px;">🪐 Planetary Positions</div>';
        for (let planetName in this.planets) {
            const planet = this.planets[planetName];
            if (planet.visible) {
                const retrogradeIcon = planet.retrograde ? ' ℞' : '';
                const color = planet.retrograde ? '#ff4444' : planet.color;
                html += `<div style="color: ${color}; margin: 2px 0;">
                    ${planet.name}: ${planet.currentAngle.toFixed(0)}°${retrogradeIcon}
                </div>`;
            }
        }
        overlay.innerHTML = html;
    },
    
    // Draw individual planet
    drawPlanet: function(g, planet) {
        try {
            // Calculate planet position in dome coordinates
            const domeCoord = this.calculateDomePosition(planet);
            
            if (domeCoord) {
                // Set planet appearance
                const color = planet.retrograde ? '#FF4444' : planet.color;
                const size = planet.size;
                
                // Draw planet glow if retrograde
                if (planet.retrograde && this.showRetrograde) {
                    g.SetAlpha(0.5);
                    g.SetMarkerAttr('Circle', size + 3, '#FF4444', '#FF4444', 1);
                    g.Marker3D(domeCoord, 2);
                    g.SetAlpha(1);
                }
                
                // Draw main planet
                g.SetMarkerAttr('Circle', size, 'black', color, 1);
                g.Marker3D(domeCoord, 3);
                
                // Draw label if zoomed in
                if (typeof FeDomeApp !== 'undefined' && FeDomeApp.Zoom > 2) {
                    const labelPos = [domeCoord[0], domeCoord[1], domeCoord[2] + 200];
                    const labelText = planet.name + (planet.retrograde ? ' ℞' : '');
                    
                    g.SetTextAttr('Arial', 10, 'white', 'normal', 'normal', 'center', 'bottom', 2);
                    g.SetAreaAttr('black', 'black', 1);
                    g.SetAlpha(0.7);
                    g.TextBox3D(labelText, labelPos, 2);
                    g.SetAlpha(1);
                    g.Text3D(labelText, labelPos);
                }
            }
        } catch (e) {
            // Silently handle any drawing errors
            console.warn('Error drawing planet:', planet.name, e);
        }
    },
    
    // Calculate dome position for planet
    calculateDomePosition: function(planet) {
        try {
            // Convert orbital angle to celestial coordinates
            const angle = planet.currentAngle * Math.PI / 180;

            // Calculate celestial latitude and longitude
            const celestLat = Math.sin(angle * 0.1) * 30; // Vary latitude slightly
            const celestLng = planet.currentAngle; // Use angle directly as longitude

            // Use existing coordinate transformation if available
            if (typeof FeDomeApp !== 'undefined' && FeDomeApp.CelestLatLongToDomeCoord && FeDomeApp.IsInit) {
                return FeDomeApp.CelestLatLongToDomeCoord(celestLat, celestLng);
            }

            // Fallback calculation for dome coordinates
            const radius = 2000 + planet.distance * 500; // Scale distance
            const x = radius * Math.cos(angle);
            const y = radius * Math.sin(angle);
            const z = 1000 + Math.sin(celestLat * Math.PI / 180) * 500;

            return [x, y, z];
        } catch (e) {
            console.warn('Error calculating dome position for:', planet.name, e);
            return null;
        }
    },
    
    // Toggle planet visibility
    togglePlanet: function(planetName, visible) {
        if (this.planets[planetName]) {
            this.planets[planetName].visible = visible;
        }
    },
    
    // Toggle retrograde display
    toggleRetrograde: function(show) {
        this.showRetrograde = show;
    },
    
    // Set planetary speed
    setSpeed: function(speed) {
        this.planetarySpeed = Math.max(0.1, Math.min(10, speed));
    },

    // Add button handler for Planets tab
    addPlanetsButtonHandler: function() {
        try {
            const planetsButton = document.getElementById('PlanetsButton');
            if (planetsButton) {
                planetsButton.addEventListener('click', () => {
                    // Toggle planets display
                    this.showPlanets = !this.showPlanets;

                    // Update button appearance
                    if (this.showPlanets) {
                        planetsButton.classList.add('TabSelected');
                        this.createControlPanel();
                        console.log('🪐 Planets enabled - showing Mercury, Venus, Mars, Jupiter, Saturn with retrograde motion');
                        console.log('📍 Current planet positions:');
                        for (let name in this.planets) {
                            const planet = this.planets[name];
                            console.log(`  ${planet.name}: ${planet.currentAngle.toFixed(1)}° ${planet.retrograde ? '(RETROGRADE ℞)' : ''}`);
                        }
                    } else {
                        planetsButton.classList.remove('TabSelected');
                        this.removeControlPanel();
                        console.log('🪐 Planets disabled');
                    }
                });
            }
        } catch (e) {
            console.warn('Error adding planets button handler:', e);
        }
    },

    // Create floating control panel
    createControlPanel: function() {
        if (document.getElementById('planetaryControlPanel')) return;

        const panel = document.createElement('div');
        panel.id = 'planetaryControlPanel';
        panel.style.cssText = `
            position: fixed;
            top: 10px;
            right: 10px;
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 10px;
            border-radius: 5px;
            font-family: Arial, sans-serif;
            font-size: 12px;
            z-index: 1000;
            min-width: 200px;
        `;

        panel.innerHTML = `
            <div style="font-weight: bold; margin-bottom: 10px;">Planetary Controls</div>
            <div style="margin-bottom: 5px;">
                <label><input type="checkbox" id="showRetrograde" ${this.showRetrograde ? 'checked' : ''}> Show Retrograde</label>
            </div>
            <div style="margin-bottom: 10px;">
                <label>Speed: <input type="range" id="planetSpeed" min="0.1" max="5" step="0.1" value="${this.planetarySpeed}" style="width: 100px;"></label>
                <span id="speedValue">${this.planetarySpeed}x</span>
            </div>
            <div style="font-size: 10px; color: #ccc;">
                Planets show realistic retrograde motion<br>
                when they appear to move backward in the sky
            </div>
        `;

        document.body.appendChild(panel);

        // Add event listeners
        document.getElementById('showRetrograde').addEventListener('change', (e) => {
            this.showRetrograde = e.target.checked;
        });

        document.getElementById('planetSpeed').addEventListener('input', (e) => {
            this.planetarySpeed = parseFloat(e.target.value);
            document.getElementById('speedValue').textContent = this.planetarySpeed + 'x';
        });
    },

    // Remove control panel
    removeControlPanel: function() {
        const panel = document.getElementById('planetaryControlPanel');
        if (panel) {
            panel.remove();
        }

        // Also remove HTML overlay
        const overlay = document.getElementById('planetaryHTMLOverlay');
        if (overlay) {
            overlay.remove();
        }
    }
};

// Auto-initialize when the page loads
function initPlanetarySystem() {
    // Wait for FeDomeApp to be fully initialized
    if (typeof FeDomeApp !== 'undefined' && FeDomeApp.IsInit && FeDomeApp.GraphObject) {
        console.log('Main app detected, initializing planetary system...');
        window.PlanetaryOverlay.init();
    } else {
        // Check again in 500ms
        setTimeout(initPlanetarySystem, 500);
    }
}

if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', function() {
        setTimeout(initPlanetarySystem, 1000);
    });
} else {
    setTimeout(initPlanetarySystem, 1000);
}
